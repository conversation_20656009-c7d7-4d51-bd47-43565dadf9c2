#!/usr/bin/env python3
"""
🚀 ULTRA HIGH-ACCURACY TRADING BOT LAUNCHER
Simple launcher for rule-based trading strategies only
"""

import os
import sys
from utils import print_colored, print_header

def show_menu():
    """Display the main menu"""
    print_header("🤖 ULTRA HIGH-ACCURACY TRADING BOT")
    print_colored("Choose an option:", "INFO", bold=True)
    print()
    print_colored("1. 📈 Start Live Trading (Ultra High-Accuracy)", "BUY", bold=True)
    print_colored("2. 🔗 Test API Connection", "INFO", bold=True)
    print_colored("3. 📊 View Current Directory", "INFO", bold=True)
    print_colored("4. ❌ Exit", "ERROR", bold=True)
    print()

def start_rule_based_trading():
    """Start the rule-based live trading bot"""
    print_header("📈 ULTRA HIGH-ACCURACY LIVE TRADING")
    print_colored("🔧 Using: Ultra High-Accuracy Strategies (85%+ confidence)", "INFO")
    print_colored("   S1: MOMENTUM BREAKOUT (Continuation) - 90% confidence", "INFO")
    print_colored("   S2: PULLBACK ENTRY (Continuation) - 88% confidence", "INFO")
    print_colored("   S3: REVERSAL SIGNALS (Pure Reversal) - 92% confidence", "INFO")
    print_colored("   S4: TREND CONFIRMATION (Strong Trend Following) - 89% confidence", "INFO")
    print_colored("📊 Signal Generation: Advanced technical analysis", "INFO")
    print_colored("⚡ Performance: Fast and transparent", "INFO")
    print_colored("⚠️  This will start live market monitoring", "WARNING", bold=True)
    print_colored("Press Ctrl+C to stop the bot at any time", "INFO")
    print()

    # Select currency pairs
    try:
        selected_pairs = select_currency_pairs()
        print()

        print_colored(f"📊 Selected {len(selected_pairs)} currency pairs for analysis", "SUCCESS")
        print_colored("⚠️  Ready to start live market monitoring", "WARNING", bold=True)
        print()

        confirm = input("Start ultra high-accuracy live trading? (y/n): ").strip().lower()
        if confirm == 'y':
            try:
                from live_trading_bot import main
                main(selected_pairs)
            except Exception as e:
                print_colored(f"❌ Error starting live trading: {str(e)}", "ERROR")
        else:
            print_colored("❌ Live trading cancelled", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error in currency pair selection: {str(e)}", "ERROR")

def select_currency_pairs():
    """Select currency pairs for trading"""
    pairs = [
        "EUR_USD", "GBP_USD", "USD_JPY", "AUD_USD", "USD_CAD",
        "AUD_CAD", "EUR_JPY", "GBP_JPY", "USD_CHF", "EUR_GBP"
    ]
    
    print_colored("💱 Available currency pairs:", "INFO", bold=True)
    for i, pair in enumerate(pairs, 1):
        print_colored(f"   {i}. {pair}", "INFO")
    print()
    
    selection = input("Enter pair numbers (comma-separated) or 'all' for all pairs:\n   Pairs: ").strip()
    
    if selection.lower() == 'all':
        selected_pairs = pairs
    else:
        try:
            indices = [int(x.strip()) - 1 for x in selection.split(',')]
            selected_pairs = [pairs[i] for i in indices if 0 <= i < len(pairs)]
        except (ValueError, IndexError):
            print_colored("❌ Invalid selection. Using EUR_USD as default.", "WARNING")
            selected_pairs = ["EUR_USD"]
    
    print_colored(f"✅ Selected pairs: {', '.join(selected_pairs)}", "SUCCESS")
    return selected_pairs

def test_api_connection():
    """Test the Oanda API connection"""
    print_header("🔗 TESTING API CONNECTION")
    print_colored("🔍 Testing Oanda API connection...", "INFO")
    print_colored("📊 Checking account details and market data access", "INFO")
    print()
    
    try:
        # Simple connection test
        from live_trading_bot import LiveTradingBot
        bot = LiveTradingBot(["EUR_USD"])
        print_colored("✅ API connection successful!", "SUCCESS")
        print_colored("✅ Account access verified", "SUCCESS")
        print_colored("✅ Market data access confirmed", "SUCCESS")
    except Exception as e:
        print_colored(f"❌ API connection failed: {str(e)}", "ERROR")
        print_colored("💡 Please check your API credentials in config.py", "WARNING")

def view_directory():
    """View current directory contents"""
    print_header("📊 CURRENT DIRECTORY CONTENTS")
    
    try:
        files = os.listdir('.')
        py_files = [f for f in files if f.endswith('.py')]
        csv_files = [f for f in files if f.endswith('.csv')]
        directories = [f for f in files if os.path.isdir(f) and not f.startswith('.')]
        
        if py_files:
            print_colored("🐍 Python Files:", "SUCCESS", bold=True)
            for f in sorted(py_files):
                print_colored(f"   {f}", "SUCCESS")
            print()
        
        if csv_files:
            print_colored("📊 Data Files:", "INFO", bold=True)
            for f in sorted(csv_files):
                print_colored(f"   {f}", "INFO")
            print()
        
        if directories:
            print_colored("📁 Directories:", "HEADER", bold=True)
            for d in sorted(directories):
                print_colored(f"   {d}/", "HEADER")
            print()
        
        print_colored(f"📈 Total files: {len(files)}", "BUY", bold=True)
        
    except Exception as e:
        print_colored(f"❌ Error viewing directory: {str(e)}", "ERROR")

def main():
    """Main launcher function"""
    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-4): ").strip()

            if choice == '1':
                start_rule_based_trading()
            elif choice == '2':
                test_api_connection()
            elif choice == '3':
                view_directory()
            elif choice == '4':
                print_colored("👋 Goodbye! Happy Trading! 🚀", "SUCCESS", bold=True)
                break
            else:
                print_colored("❌ Invalid choice. Please enter 1-4.", "ERROR")

            # Wait for user to continue
            if choice in ['1', '2', '3']:
                print()
                input("Press Enter to continue...")
                print()

        except KeyboardInterrupt:
            print_colored("\n👋 Goodbye! Happy Trading! 🚀", "SUCCESS", bold=True)
            break
        except Exception as e:
            print_colored(f"❌ Error in main menu: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
