#!/usr/bin/env python3

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_engine import StrategyEngine
from utils import fetch_live_candles, add_technical_indicators
from config import TRADING_CONFIG

def run_direct_backtest():
    """Run a direct backtest to test simplified strategies"""
    print("🔬 DIRECT BACKTEST - Testing Simplified Strategies")
    print("="*60)
    
    try:
        # Initialize strategy engine
        engine = StrategyEngine()
        print("✅ Strategy engine initialized")
        
        # Fetch real data
        print("📊 Fetching EUR_USD data...")

        df = fetch_live_candles("EUR_USD", 600)  # Fetch 600 candles (10 hours)
        print(f"✅ Fetched {len(df)} candles")
        
        if len(df) < 50:
            print("❌ Not enough data")
            return
        
        # Add technical indicators
        df = add_technical_indicators(df)
        print("✅ Technical indicators added")
        
        # Test strategies on real data
        print(f"\n🔍 Testing strategies on {len(df)} candles...")
        print(f"📊 Time range: {df['time'].iloc[0]} to {df['time'].iloc[-1]}")
        print(f"📊 MIN_CONFIDENCE: {TRADING_CONFIG['MIN_CONFIDENCE']}")
        
        signals_found = []
        
        # Check each candle for signals
        for i in range(50, len(df)):  # Start from index 50 for enough history
            current_df = df.iloc[:i+1].copy()
            
            try:
                result = engine.evaluate_all_strategies(current_df)
                
                if result and result['signal'] != 'HOLD' and result['signal'] != 0:
                    signal_info = {
                        'time': current_df['time'].iloc[-1],
                        'price': current_df['close'].iloc[-1],
                        'signal': result['signal'],
                        'confidence': result['confidence'],
                        'strategy': result['strategy']
                    }
                    signals_found.append(signal_info)
                    
                    print(f"📈 SIGNAL FOUND!")
                    print(f"  Time: {signal_info['time']}")
                    print(f"  Price: {signal_info['price']:.5f}")
                    print(f"  Signal: {signal_info['signal']}")
                    print(f"  Confidence: {signal_info['confidence']:.3f}")
                    print(f"  Strategy: {signal_info['strategy']}")
                    print()
                    
                    if len(signals_found) >= 10:  # Limit output
                        break
                        
            except Exception as e:
                print(f"❌ Error at index {i}: {e}")
                continue
        
        print("="*60)
        print(f"📊 BACKTEST RESULTS")
        print(f"📊 Total signals found: {len(signals_found)}")
        
        if len(signals_found) == 0:
            print("❌ NO SIGNALS GENERATED - Strategies still too strict!")
            print("🔧 Need to make strategies even more lenient...")
        else:
            print("✅ Strategies are now generating signals!")
            
            # Show signal distribution
            buy_signals = sum(1 for s in signals_found if s['signal'] == 1 or s['signal'] == 'BUY')
            sell_signals = sum(1 for s in signals_found if s['signal'] == -1 or s['signal'] == 'SELL')
            
            print(f"📈 BUY signals: {buy_signals}")
            print(f"📉 SELL signals: {sell_signals}")
            
            # Show strategy distribution
            strategy_counts = {}
            for signal in signals_found:
                strategy = signal['strategy']
                strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
            
            print(f"📊 Strategy distribution:")
            for strategy, count in strategy_counts.items():
                print(f"  {strategy}: {count} signals")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_direct_backtest()
