#!/usr/bin/env python3
"""
Test script for new trading strategies
"""

import pandas as pd
import numpy as np
from strategy_engine import StrategyEngine
from utils import print_colored, add_technical_indicators

def create_sample_data(num_candles=100):
    """Create sample OHLCV data for testing"""
    np.random.seed(42)
    
    # Generate realistic price data
    base_price = 1.1000
    prices = [base_price]
    
    for i in range(num_candles - 1):
        change = np.random.normal(0, 0.0001)  # Small random changes
        new_price = prices[-1] + change
        prices.append(max(new_price, 0.5))  # Prevent negative prices
    
    # Create OHLCV data
    data = []
    for i, close in enumerate(prices):
        high = close + abs(np.random.normal(0, 0.00005))
        low = close - abs(np.random.normal(0, 0.00005))
        open_price = low + (high - low) * np.random.random()
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'timestamp': pd.Timestamp.now() + pd.<PERSON><PERSON><PERSON>(minutes=i),
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    return df

def test_strategies():
    """Test all four new strategies"""
    print_colored("🧪 Testing New Trading Strategies", "HEADER", bold=True)
    print()
    
    try:
        # Create sample data
        print_colored("📊 Creating sample market data...", "INFO")
        df = create_sample_data(100)
        
        # Add technical indicators
        print_colored("📈 Adding technical indicators...", "INFO")
        df = add_technical_indicators(df)
        
        # Initialize strategy engine
        print_colored("🔧 Initializing strategy engine...", "INFO")
        
        # Mock the model loading to avoid file not found errors
        class MockStrategyEngine(StrategyEngine):
            def load_models(self):
                print_colored("⚠️  Skipping model loading for testing", "WARNING")
                pass
        
        engine = MockStrategyEngine()
        
        # Test each strategy
        strategies = [
            ("Strategy 1: MOMENTUM BREAKOUT (Continuation)", engine.evaluate_strategy_1),
            ("Strategy 2: PULLBACK ENTRY (Continuation)", engine.evaluate_strategy_2),
            ("Strategy 3: REVERSAL SIGNALS (Pure Reversal)", engine.evaluate_strategy_3),
            ("Strategy 4: TREND CONFIRMATION (Strong Trend Following)", engine.evaluate_strategy_4)
        ]
        
        print()
        print_colored("🔍 Testing Strategies:", "HEADER", bold=True)
        print()
        
        for strategy_name, strategy_func in strategies:
            try:
                signal, confidence = strategy_func(df)
                
                if signal == 1:
                    signal_text = "BUY"
                    color = "BUY"
                elif signal == -1:
                    signal_text = "SELL"
                    color = "SELL"
                else:
                    signal_text = "HOLD"
                    color = "INFO"
                
                print_colored(f"✅ {strategy_name}", "SUCCESS")
                print_colored(f"   Signal: {signal_text} | Confidence: {confidence:.2%}", color)
                print()
                
            except Exception as e:
                print_colored(f"❌ {strategy_name}", "ERROR")
                print_colored(f"   Error: {str(e)}", "ERROR")
                print()
        
        # Test combined evaluation
        print_colored("🎯 Testing Combined Strategy Evaluation:", "HEADER", bold=True)
        try:
            result = engine.evaluate_all_strategies(df)
            
            signal_color = "BUY" if result['signal'] == "BUY" else "SELL" if result['signal'] == "SELL" else "INFO"
            
            print_colored(f"Combined Signal: {result['signal']}", signal_color, bold=True)
            print_colored(f"Confidence: {result['confidence']:.2%}", "INFO")
            print_colored(f"Best Strategy: {result['strategy']}", "SUCCESS")
            print_colored(f"Price: {result['price']:.5f}", "INFO")
            
            print()
            print_colored("Individual Strategy Results:", "INFO")
            for strategy, data in result['all_signals'].items():
                signal_text = "BUY" if data['signal'] == 1 else "SELL" if data['signal'] == -1 else "HOLD"
                print_colored(f"  {strategy}: {signal_text} ({data['confidence']:.2%})", "INFO")
            
        except Exception as e:
            print_colored(f"❌ Combined evaluation failed: {str(e)}", "ERROR")
        
        print()
        print_colored("✅ Strategy testing completed successfully!", "SUCCESS", bold=True)
        
    except Exception as e:
        print_colored(f"❌ Testing failed: {str(e)}", "ERROR")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_strategies()
