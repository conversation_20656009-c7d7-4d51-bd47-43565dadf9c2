#!/usr/bin/env python3

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """Create test data with clear patterns"""
    dates = pd.date_range(start='2025-07-02 11:00', end='2025-07-02 12:00', freq='1min')
    
    data = []
    base_price = 1.0500
    
    for i, date in enumerate(dates):
        # Create clear uptrend
        price = base_price + (i * 0.0002)  # Strong uptrend
        
        data.append({
            'time': date,
            'open': price - 0.00005,
            'high': price + 0.0001,
            'low': price - 0.0001,
            'close': price,
            'volume': 500 + (i * 20)  # Increasing volume
        })
    
    df = pd.DataFrame(data)
    
    # Add basic technical indicators manually
    df['ema_20'] = df['close'].ewm(span=20).mean()
    df['rsi'] = 50 + (np.random.random(len(df)) - 0.5) * 40  # Random RSI 30-70
    df['volume_avg'] = df['volume'].rolling(20).mean()
    
    return df

def test_individual_strategies():
    """Test each strategy individually"""
    print("🔧 Testing Individual Strategies")
    print("="*50)
    
    try:
        from strategy_engine import StrategyEngine
        from config import TRADING_CONFIG
        
        print(f"📊 MIN_CONFIDENCE: {TRADING_CONFIG['MIN_CONFIDENCE']}")
        
        engine = StrategyEngine()
        df = create_test_data()
        
        print(f"📊 Created {len(df)} candles")
        print(f"📊 Price trend: {df['close'].iloc[0]:.5f} -> {df['close'].iloc[-1]:.5f}")
        print(f"📊 Volume trend: {df['volume'].iloc[0]} -> {df['volume'].iloc[-1]}")
        
        # Test individual strategy methods
        strategies = [
            ('S1', engine.evaluate_strategy_1),
            ('S2', engine.evaluate_strategy_2),
            ('S3', engine.evaluate_strategy_3),
            ('S4', engine.evaluate_strategy_4)
        ]
        
        for strategy_name, strategy_func in strategies:
            print(f"\n🔍 Testing {strategy_name}...")
            try:
                signal, confidence = strategy_func(df)
                print(f"  📊 Signal: {signal}")
                print(f"  📊 Confidence: {confidence:.3f}")
                
                if signal != 0:
                    print(f"  ✅ {strategy_name} GENERATED SIGNAL!")
                else:
                    print(f"  ❌ {strategy_name} no signal")
                    
            except Exception as e:
                print(f"  ❌ Error in {strategy_name}: {e}")
                import traceback
                traceback.print_exc()
        
        # Test combined evaluation
        print(f"\n🔍 Testing combined evaluation...")
        try:
            result = engine.evaluate_all_strategies(df)
            print(f"  📊 Combined result: {result}")
            
            if result and result['signal'] != 'HOLD' and result['signal'] != 0:
                print(f"  ✅ COMBINED SIGNAL GENERATED!")
                print(f"    Signal: {result['signal']}")
                print(f"    Confidence: {result['confidence']:.3f}")
                print(f"    Strategy: {result['strategy']}")
            else:
                print(f"  ❌ No combined signal")
                
        except Exception as e:
            print(f"  ❌ Error in combined evaluation: {e}")
            import traceback
            traceback.print_exc()
        
        # Test with even more obvious data
        print(f"\n🔧 Testing with EXTREME patterns...")
        
        # Create extreme uptrend
        extreme_data = []
        for i in range(30):
            price = 1.0500 + (i * 0.001)  # HUGE moves
            
            extreme_data.append({
                'time': pd.Timestamp('2025-07-02 13:00') + pd.Timedelta(minutes=i),
                'open': price - 0.0002,
                'high': price + 0.0003,
                'low': price - 0.0003,
                'close': price,
                'volume': 2000 + (i * 100)  # High volume
            })
        
        extreme_df = pd.DataFrame(extreme_data)
        extreme_df['ema_20'] = extreme_df['close'].ewm(span=20).mean()
        extreme_df['rsi'] = 60 + (np.random.random(len(extreme_df)) - 0.5) * 20  # RSI 50-70
        extreme_df['volume_avg'] = extreme_df['volume'].rolling(20).mean()
        
        print(f"📊 Extreme pattern: {extreme_df['close'].iloc[0]:.5f} -> {extreme_df['close'].iloc[-1]:.5f}")
        
        for strategy_name, strategy_func in strategies:
            print(f"\n🔍 Testing {strategy_name} with extreme pattern...")
            try:
                signal, confidence = strategy_func(extreme_df)
                print(f"  📊 Signal: {signal}, Confidence: {confidence:.3f}")
                
                if signal != 0:
                    print(f"  ✅ {strategy_name} WORKS WITH EXTREME PATTERN!")
                else:
                    print(f"  ❌ {strategy_name} still no signal - TOO STRICT!")
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_individual_strategies()
