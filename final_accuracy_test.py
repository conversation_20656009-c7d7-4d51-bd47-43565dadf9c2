#!/usr/bin/env python3
"""
Final Accuracy Test - Test balanced strategies with realistic conditions
"""

import pandas as pd
import numpy as np
from strategy_engine import StrategyEngine

def create_realistic_data():
    """Create realistic market data with various patterns"""
    print("📊 Creating realistic market data...")
    
    np.random.seed(42)
    base_price = 1.0850
    data = []
    current_price = base_price
    
    # Create 200 candles with realistic patterns
    for i in range(200):
        # Different market phases
        if i < 40:  # Sideways consolidation
            change = np.random.uniform(-0.00003, 0.00003)
            vol_mult = np.random.uniform(0.8, 1.2)
        elif i < 80:  # Strong uptrend with volume
            change = np.random.uniform(0.00002, 0.0001)
            vol_mult = np.random.uniform(1.2, 2.0)
        elif i < 120:  # Pullback phase
            change = np.random.uniform(-0.00006, 0.00002)
            vol_mult = np.random.uniform(0.9, 1.4)
        elif i < 160:  # Reversal at resistance
            if i < 140:
                change = np.random.uniform(-0.00001, 0.00008)
                vol_mult = np.random.uniform(1.1, 1.8)
            else:  # Rejection and reversal
                change = np.random.uniform(-0.0001, -0.00002)
                vol_mult = np.random.uniform(1.5, 2.5)
        else:  # New trend formation
            change = np.random.uniform(-0.00008, 0.00004)
            vol_mult = np.random.uniform(1.0, 1.6)
        
        open_price = current_price
        close_price = open_price + change
        
        # Create realistic wicks
        volatility = np.random.uniform(0.00002, 0.0001)
        high_price = max(open_price, close_price) + volatility
        low_price = min(open_price, close_price) - volatility
        
        # Volume with realistic patterns
        base_volume = 2000
        volume = int(base_volume * vol_mult)
        
        data.append({
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'volume': volume
        })
        
        current_price = close_price
    
    df = pd.DataFrame(data)
    
    # Add technical indicators
    df['rsi'] = calculate_rsi(df['close'])
    df['ema_20'] = df['close'].ewm(span=20).mean()
    
    print(f"✅ Created {len(df)} candles")
    print(f"   Price range: {df['close'].min():.5f} - {df['close'].max():.5f}")
    print(f"   Volume range: {df['volume'].min()} - {df['volume'].max()}")
    print(f"   RSI range: {df['rsi'].min():.1f} - {df['rsi'].max():.1f}")
    
    return df

def calculate_rsi(prices, period=14):
    """Calculate RSI"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.fillna(50)

def test_balanced_strategies():
    """Test the balanced strategies"""
    print("🧪 TESTING BALANCED STRATEGY ACCURACY")
    print("=" * 50)
    
    # Create realistic test data
    df = create_realistic_data()
    
    # Initialize engine
    engine = StrategyEngine()
    
    strategies = [
        ("S1: MOMENTUM BREAKOUT", engine.evaluate_strategy_1),
        ("S2: PULLBACK ENTRY", engine.evaluate_strategy_2),
        ("S3: REVERSAL SIGNALS", engine.evaluate_strategy_3),
        ("S4: TREND CONFIRMATION", engine.evaluate_strategy_4)
    ]
    
    results = {}
    total_signals = 0
    total_wins = 0
    
    # Test each strategy
    for name, strategy_func in strategies:
        print(f"\n🔍 Testing {name}...")
        
        signals = []
        wins = 0
        losses = 0
        
        # Test on sliding window (skip first 50 for indicators)
        for i in range(50, len(df) - 10):
            test_df = df.iloc[:i+1].copy()
            
            try:
                signal, confidence = strategy_func(test_df)
                
                if signal != 0 and confidence >= 0.90:  # Only high confidence signals
                    current_price = test_df.iloc[-1]['close']
                    
                    # Check next 10 candles for profit (more realistic timeframe)
                    future_prices = df.iloc[i+1:i+11]['close']
                    
                    if signal == 1:  # BUY
                        max_price = future_prices.max()
                        profit = (max_price - current_price) / current_price
                        win = profit > 0.0005  # 0.05% target (realistic for forex)
                    else:  # SELL
                        min_price = future_prices.min()
                        profit = (current_price - min_price) / current_price
                        win = profit > 0.0005  # 0.05% target
                    
                    signal_info = {
                        'candle': i,
                        'type': 'BUY' if signal == 1 else 'SELL',
                        'confidence': confidence,
                        'price': current_price,
                        'profit': profit,
                        'win': win
                    }
                    signals.append(signal_info)
                    
                    if win:
                        wins += 1
                    else:
                        losses += 1
                        
            except Exception as e:
                continue
        
        # Calculate results
        total = wins + losses
        accuracy = (wins / total * 100) if total > 0 else 0
        
        results[name] = {
            'total': total,
            'wins': wins,
            'losses': losses,
            'accuracy': accuracy,
            'signals': signals
        }
        
        total_signals += total
        total_wins += wins
        
        # Print results
        if total > 0:
            print(f"  📊 Total Signals: {total}")
            print(f"  ✅ Wins: {wins}")
            print(f"  ❌ Losses: {losses}")
            print(f"  🎯 Accuracy: {accuracy:.1f}%")
            
            # Show sample signals
            if signals:
                print(f"  📋 Sample Signals:")
                for signal in signals[-2:]:  # Show last 2 signals
                    result = "WIN" if signal['win'] else "LOSS"
                    color = "✅" if signal['win'] else "❌"
                    print(f"    {color} {signal['type']} @ {signal['price']:.5f} "
                          f"({signal['confidence']:.1%}) - {result} ({signal['profit']:+.4f})")
        else:
            print(f"  ⚠️  No signals generated")
    
    # Overall summary
    if total_signals > 0:
        overall_accuracy = (total_wins / total_signals) * 100
        print(f"\n🎯 OVERALL PERFORMANCE:")
        print(f"  Total Signals: {total_signals}")
        print(f"  Total Wins: {total_wins}")
        print(f"  Total Losses: {total_signals - total_wins}")
        print(f"  Overall Accuracy: {overall_accuracy:.1f}%")
        
        # Final assessment
        print(f"\n💡 ASSESSMENT:")
        if overall_accuracy >= 60:
            print("  ✅ EXCELLENT - High accuracy achieved!")
        elif overall_accuracy >= 50:
            print("  ✅ GOOD - Acceptable accuracy for live trading")
        elif overall_accuracy >= 40:
            print("  ⚠️  MODERATE - Consider further optimization")
        else:
            print("  ❌ POOR - Strategies need more work")
        
        return results
    else:
        print(f"\n⚠️  NO SIGNALS GENERATED")
        print("  Strategies may be too strict or market conditions not suitable")
        return results

if __name__ == "__main__":
    test_balanced_strategies()
