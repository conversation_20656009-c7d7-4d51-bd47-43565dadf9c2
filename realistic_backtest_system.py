#!/usr/bin/env python3
"""
Realistic Backtesting System
Uses EXACT same logic as live bot to ensure identical signals
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from strategy_engine import StrategyEngine
from utils import (
    fetch_live_candles, add_technical_indicators, print_colored, 
    print_header, format_price, get_current_time_info
)
from config import CURRENCY_PAIRS, TRADING_CONFIG

class RealisticBacktester:
    def __init__(self):
        """Initialize the realistic backtester"""
        self.strategy_engine = StrategyEngine()
        self.results = {}
        
    def select_strategies(self):
        """Allow user to select which strategies to test"""
        print_header("📊 STRATEGY SELECTION")
        print_colored("Available Strategies:", "INFO", bold=True)
        print_colored("1. S1 - MOMENTUM BREAKOUT", "INFO")
        print_colored("2. S2 - PULLBACK ENTRY", "INFO") 
        print_colored("3. S3 - BINARY REVERSAL", "INFO")
        print_colored("4. S4 - TREND CONFIRMATION", "INFO")
        print_colored("5. ALL STRATEGIES", "SUCCESS", bold=True)
        print()
        
        while True:
            choice = input("Select strategies (1-5): ").strip()
            if choice == '1':
                return ['S1']
            elif choice == '2':
                return ['S2']
            elif choice == '3':
                return ['S3']
            elif choice == '4':
                return ['S4']
            elif choice == '5':
                return ['S1', 'S2', 'S3', 'S4']
            else:
                print_colored("❌ Invalid choice. Please enter 1-5.", "ERROR")

    def select_currency_pairs(self):
        """Allow user to select multiple currency pairs"""
        print_header("💱 CURRENCY PAIR SELECTION")
        print_colored("Available Currency Pairs:", "INFO", bold=True)
        for i, pair in enumerate(CURRENCY_PAIRS, 1):
            print_colored(f"{i}. {pair}", "INFO")
        print_colored(f"{len(CURRENCY_PAIRS) + 1}. ALL PAIRS", "SUCCESS", bold=True)
        print()

        while True:
            try:
                choices = input(f"Select currency pairs (comma-separated, e.g., 1,3,5 or {len(CURRENCY_PAIRS) + 1} for all): ").strip()

                if choices == str(len(CURRENCY_PAIRS) + 1):
                    return CURRENCY_PAIRS

                selected_indices = [int(x.strip()) for x in choices.split(',')]
                selected_pairs = []

                for idx in selected_indices:
                    if 1 <= idx <= len(CURRENCY_PAIRS):
                        selected_pairs.append(CURRENCY_PAIRS[idx - 1])
                    else:
                        print_colored(f"❌ Invalid choice: {idx}. Please enter numbers 1-{len(CURRENCY_PAIRS)}.", "ERROR")
                        break
                else:
                    if selected_pairs:
                        return selected_pairs

            except ValueError:
                print_colored("❌ Please enter valid numbers separated by commas.", "ERROR")

    def get_time_range(self):
        """Get time range for backtesting"""
        print_header("⏰ TIME RANGE SELECTION")
        print_colored("Enter the time range for backtesting:", "INFO", bold=True)
        print_colored("Format: YYYY-MM-DD HH:MM (24-hour format)", "INFO")
        print_colored("Example: 2024-01-15 09:00", "INFO")
        print()
        
        while True:
            try:
                start_str = input("Start time: ").strip()
                end_str = input("End time: ").strip()
                
                start_time = datetime.strptime(start_str, "%Y-%m-%d %H:%M")
                end_time = datetime.strptime(end_str, "%Y-%m-%d %H:%M")
                
                if start_time >= end_time:
                    print_colored("❌ Start time must be before end time.", "ERROR")
                    continue
                
                # Calculate duration
                duration = end_time - start_time
                hours = duration.total_seconds() / 3600
                
                print_colored(f"✅ Time range: {start_time} to {end_time}", "SUCCESS")
                print_colored(f"📊 Duration: {hours:.1f} hours ({duration.days} days)", "INFO")
                
                confirm = input("Confirm this time range? (y/n): ").strip().lower()
                if confirm == 'y':
                    return start_time, end_time
                    
            except ValueError:
                print_colored("❌ Invalid time format. Use YYYY-MM-DD HH:MM", "ERROR")

    def fetch_historical_data(self, pair, start_time, end_time):
        """Fetch historical data for the specified time range"""
        print_colored(f"📊 Fetching historical data for {pair}...", "INFO")
        
        # Calculate required candles (assuming 1-minute candles)
        duration = end_time - start_time
        required_candles = int(duration.total_seconds() / 60) + 100  # Extra for indicators
        
        # Fetch data (using live candles function as proxy for historical)
        df = fetch_live_candles(pair, required_candles)
        
        if df is None or len(df) == 0:
            print_colored(f"❌ Failed to fetch data for {pair}", "ERROR")
            return None
            
        print_colored(f"✅ Fetched {len(df)} candles for {pair}", "SUCCESS")
        return df

    def run_backtest(self, df, selected_strategies, pair, start_time, end_time):
        """Run backtest using EXACT same logic as live bot"""
        print_header(f"🔬 RUNNING BACKTEST - {pair}")
        print_colored(f"📊 Testing strategies: {', '.join(selected_strategies)}", "INFO")
        print_colored(f"⏰ Time range: {start_time} to {end_time}", "INFO")
        print_colored(f"🎯 Min confidence: {TRADING_CONFIG['MIN_CONFIDENCE']}", "INFO")
        print()
        
        all_signals = []
        strategy_results = {strategy: {'signals': [], 'wins': 0, 'losses': 0} for strategy in selected_strategies}
        
        # Check each candle close for signals (like you explained)
        for i in range(50, len(df) - 1):  # -1 because we need next candle to check result
            # At candle i close (e.g., 12:05 candle closes)
            window_df = df.iloc[:i+1].copy()
            current_candle = df.iloc[i]
            next_candle = df.iloc[i+1]

            # Check if any signal generated at this candle close
            signal_data = self.strategy_engine.evaluate_all_strategies(window_df)

            # Only process actual signals (not HOLD)
            if signal_data['signal'] != 'HOLD':
                strategy = signal_data['strategy']

                # Only include if this strategy is selected for testing
                if strategy in selected_strategies:
                    # Signal time is when candle closes (e.g., 12:05:00)
                    if 'time' in current_candle:
                        signal_timestamp = current_candle['time']
                    elif 'timestamp' in current_candle:
                        signal_timestamp = current_candle['timestamp']
                    elif hasattr(current_candle, 'name') and current_candle.name is not None:
                        signal_timestamp = current_candle.name  # If timestamp is the index
                    else:
                        signal_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Fallback

                    # Determine what direction signal predicts
                    predicted_direction = signal_data['signal']  # BUY or SELL

                    # Check next candle actual direction
                    next_actual_direction = 'UP' if next_candle['close'] > next_candle['open'] else 'DOWN'

                    # Calculate if WIN or LOSS
                    if ((predicted_direction == 'BUY' and next_actual_direction == 'UP') or
                        (predicted_direction == 'SELL' and next_actual_direction == 'DOWN')):
                        # Check minimum movement (1 pip)
                        price_change_pct = abs((next_candle['close'] - next_candle['open']) / next_candle['open']) * 100
                        if price_change_pct > 0.01:  # 1 pip minimum
                            result = 'WIN'
                        else:
                            result = 'LOSS'  # Too small movement
                    else:
                        result = 'LOSS'  # Wrong direction

                    signal_info = {
                        'candle_index': i,
                        'timestamp': signal_timestamp,
                        'pair': pair,
                        'signal': predicted_direction,
                        'actual_direction': next_actual_direction,
                        'confidence': signal_data['confidence'],
                        'strategy': strategy,
                        'signal_price': current_candle['close'],
                        'next_candle_result': f"{next_candle['open']:.5f} → {next_candle['close']:.5f}",
                        'result': result
                    }

                    all_signals.append(signal_info)
                    strategy_results[strategy]['signals'].append(signal_info)

                    # Update win/loss counts
                    if result == 'WIN':
                        strategy_results[strategy]['wins'] += 1
                    else:
                        strategy_results[strategy]['losses'] += 1
        
        # Win/loss calculation is now done above during signal processing
        
        return all_signals, strategy_results

    def display_results(self, all_signals, strategy_results, pair):
        """Display detailed backtest results"""
        print_header(f"📊 BACKTEST RESULTS - {pair}")
        
        if not all_signals:
            print_colored("❌ No signals found in the specified time range", "ERROR")
            return
        
        # Display signals for each strategy
        for strategy in strategy_results.keys():
            signals = strategy_results[strategy]['signals']
            wins = strategy_results[strategy]['wins']
            losses = strategy_results[strategy]['losses']
            
            if not signals:
                print_colored(f"\n📊 {strategy}: No signals found", "WARNING")
                continue
                
            print_colored(f"\n📊 {strategy} SIGNALS:", "INFO", bold=True)
            print_colored("-" * 80, "INFO")
            
            for i, signal in enumerate(signals, 1):
                # Handle timestamp formatting
                timestamp = signal['timestamp']
                if hasattr(timestamp, 'strftime'):
                    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                elif isinstance(timestamp, str):
                    timestamp = timestamp
                else:
                    timestamp = str(timestamp)

                result_color = "SUCCESS" if signal['result'] == 'WIN' else "ERROR" if signal['result'] == 'LOSS' else "WARNING"
                
                predicted = signal['signal']
                actual = signal.get('actual_direction', 'N/A')
                next_candle = signal.get('next_candle_result', 'N/A')
                signal_price = signal.get('signal_price', 0)

                print_colored(f"{i:2d}. {timestamp} | Signal: {predicted:4s} | Next Candle: {actual:4s} | "
                            f"Price: {signal_price:.5f} | Movement: {next_candle} | Conf: {signal['confidence']:.3f} | "
                            f"Result: {signal['result']}", result_color)
            
            # Strategy summary
            total_trades = wins + losses
            accuracy = (wins / total_trades * 100) if total_trades > 0 else 0
            
            print_colored(f"\n📈 {strategy} SUMMARY:", "INFO", bold=True)
            print_colored(f"   Total Signals: {len(signals)}", "INFO")
            print_colored(f"   ✅ Wins: {wins}", "SUCCESS")
            print_colored(f"   ❌ Losses: {losses}", "ERROR")
            print_colored(f"   🎯 Accuracy: {accuracy:.1f}%", "SUCCESS" if accuracy >= 60 else "WARNING")
        
        # Overall summary
        total_signals = len(all_signals)
        total_wins = sum(strategy_results[s]['wins'] for s in strategy_results)
        total_losses = sum(strategy_results[s]['losses'] for s in strategy_results)
        overall_accuracy = (total_wins / (total_wins + total_losses) * 100) if (total_wins + total_losses) > 0 else 0
        
        print_colored(f"\n🏆 OVERALL RESULTS:", "HEADER", bold=True)
        print_colored("=" * 50, "HEADER")
        print_colored(f"📊 Total Signals: {total_signals}", "INFO")
        print_colored(f"✅ Total Wins: {total_wins}", "SUCCESS")
        print_colored(f"❌ Total Losses: {total_losses}", "ERROR")
        print_colored(f"🎯 Overall Accuracy: {overall_accuracy:.1f}%", "SUCCESS" if overall_accuracy >= 60 else "WARNING")
        
        print_colored(f"\n✅ These are the EXACT same signals the live bot would generate!", "SUCCESS", bold=True)

    def show_combined_summary(self, all_pair_results):
        """Show combined results across all currency pairs"""
        print_colored(f"\n🌍 COMBINED RESULTS ACROSS ALL PAIRS:", "HEADER", bold=True)
        print_colored("=" * 60, "HEADER")

        total_signals = 0
        total_wins = 0
        total_losses = 0

        for pair, data in all_pair_results.items():
            pair_signals = len(data['signals'])
            pair_wins = sum(data['results'][s]['wins'] for s in data['results'])
            pair_losses = sum(data['results'][s]['losses'] for s in data['results'])
            pair_accuracy = (pair_wins / (pair_wins + pair_losses) * 100) if (pair_wins + pair_losses) > 0 else 0

            print_colored(f"📊 {pair}: {pair_signals} signals, {pair_accuracy:.1f}% accuracy", "INFO")

            total_signals += pair_signals
            total_wins += pair_wins
            total_losses += pair_losses

        overall_accuracy = (total_wins / (total_wins + total_losses) * 100) if (total_wins + total_losses) > 0 else 0

        print_colored(f"\n🏆 GRAND TOTAL:", "SUCCESS", bold=True)
        print_colored(f"📊 Total Signals: {total_signals}", "INFO")
        print_colored(f"✅ Total Wins: {total_wins}", "SUCCESS")
        print_colored(f"❌ Total Losses: {total_losses}", "ERROR")
        print_colored(f"🎯 Overall Accuracy: {overall_accuracy:.1f}%", "SUCCESS" if overall_accuracy >= 60 else "WARNING")

def main():
    """Main backtesting function"""
    try:
        backtester = RealisticBacktester()
        
        # Get user selections
        selected_strategies = backtester.select_strategies()
        pairs = backtester.select_currency_pairs()
        start_time, end_time = backtester.get_time_range()
        
        # Run backtest for each pair
        all_pair_results = {}

        for pair in pairs:
            print_colored(f"\n📊 Processing {pair}...", "INFO", bold=True)

            # Fetch historical data
            df = backtester.fetch_historical_data(pair, start_time, end_time)
            if df is None:
                continue

            # Run backtest
            all_signals, strategy_results = backtester.run_backtest(
                df, selected_strategies, pair, start_time, end_time
            )

            # Display results for this pair
            backtester.display_results(all_signals, strategy_results, pair)
            all_pair_results[pair] = {'signals': all_signals, 'results': strategy_results}

            # Add separator if multiple pairs
            if len(pairs) > 1:
                print("\n" + "="*80 + "\n")

        # Show combined summary if multiple pairs
        if len(pairs) > 1:
            backtester.show_combined_summary(all_pair_results)
        
    except KeyboardInterrupt:
        print_colored("\n⚠️ Backtest interrupted by user", "WARNING")
    except Exception as e:
        import traceback
        print_colored(f"❌ Error in backtesting: {str(e)}", "ERROR")
        print_colored(f"📋 Full traceback:", "ERROR")
        traceback.print_exc()

if __name__ == "__main__":
    main()
