#!/usr/bin/env python3
"""
Basic test to check if strategies are working
"""

import pandas as pd
import numpy as np
from strategy_engine import StrategyEngine

def test_basic():
    print("🧪 Basic Strategy Test")
    print("=" * 30)
    
    # Create simple test data
    data = []
    for i in range(50):
        data.append({
            'open': 1.0850 + i * 0.0001,
            'high': 1.0850 + i * 0.0001 + 0.0002,
            'low': 1.0850 + i * 0.0001 - 0.0001,
            'close': 1.0850 + i * 0.0001 + 0.00005,
            'volume': 2000 + i * 10
        })
    
    df = pd.DataFrame(data)
    
    # Add indicators
    df['rsi'] = 50 + np.sin(np.arange(len(df)) * 0.3) * 20
    df['ema_20'] = df['close'].ewm(span=20).mean()
    
    print(f"✅ Created {len(df)} test candles")
    
    # Test engine
    try:
        engine = StrategyEngine()
        engine.load_models()
        print("✅ Engine loaded successfully")
        
        # Test each strategy
        strategies = [
            ("S1", engine.evaluate_strategy_1),
            ("S2", engine.evaluate_strategy_2),
            ("S3", engine.evaluate_strategy_3),
            ("S4", engine.evaluate_strategy_4)
        ]
        
        for name, func in strategies:
            try:
                signal, conf = func(df)
                print(f"✅ {name}: Signal={signal}, Confidence={conf:.2f}")
            except Exception as e:
                print(f"❌ {name}: Error - {str(e)}")
        
        print("\n🎯 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Engine error: {str(e)}")

if __name__ == "__main__":
    test_basic()
