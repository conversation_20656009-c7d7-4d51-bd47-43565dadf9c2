#!/usr/bin/env python3
"""
Signal Accuracy Tester - Analyze win/loss rates of trading signals
"""

import pandas as pd
import numpy as np
from strategy_engine import StrategyEngine
from utils import print_colored
import requests
from config import OANDA_CONFIG
import time

class SignalAccuracyTester:
    def __init__(self):
        self.engine = StrategyEngine()
        self.engine.load_models()
        self.signals_tested = []
        
    def create_realistic_market_data(self, pair="EUR_USD", count=200):
        """Create realistic market data for testing"""
        print_colored(f"📊 Creating realistic market data for {pair}...", "INFO")

        # Base prices for different pairs
        base_prices = {
            "EUR_USD": 1.0850,
            "GBP_USD": 1.2650,
            "USD_JPY": 149.50
        }

        base_price = base_prices.get(pair, 1.0850)
        np.random.seed(42)  # For reproducible results

        data = []
        current_price = base_price

        for i in range(count):
            # Create realistic price movements
            # Random walk with occasional trends and reversals
            if i % 50 == 0:  # Trend change every 50 candles
                trend_strength = np.random.uniform(-0.0002, 0.0002)
            else:
                trend_strength = trend_strength * 0.98  # Decay trend

            # Add noise and trend
            price_change = np.random.normal(0, 0.0001) + trend_strength

            open_price = current_price
            close_price = open_price + price_change

            # Create realistic high/low with occasional spikes
            volatility = np.random.uniform(0.00005, 0.0003)
            high_price = max(open_price, close_price) + volatility
            low_price = min(open_price, close_price) - volatility

            # Occasional large moves (news events)
            if np.random.random() < 0.02:  # 2% chance
                spike = np.random.uniform(-0.001, 0.001)
                if spike > 0:
                    high_price += spike
                else:
                    low_price += spike

            volume = int(np.random.uniform(800, 3000))

            data.append({
                'timestamp': f"2024-01-01 {i//60:02d}:{i%60:02d}:00",
                'open': round(open_price, 5),
                'high': round(high_price, 5),
                'low': round(low_price, 5),
                'close': round(close_price, 5),
                'volume': volume
            })

            current_price = close_price

        df = pd.DataFrame(data)

        # Add technical indicators
        df['rsi'] = self.calculate_rsi(df['close'])
        df['ema_20'] = df['close'].ewm(span=20).mean()

        print_colored(f"✅ Created {len(df)} candles of realistic market data", "SUCCESS")
        return df
    
    def calculate_rsi(self, prices, period=14):
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.fillna(50)
    
    def test_signal_accuracy(self, df, lookforward_candles=5):
        """Test accuracy of signals by looking forward"""
        print_colored("🔍 Testing signal accuracy...", "INFO")
        
        results = {
            'S1': {'total': 0, 'wins': 0, 'losses': 0, 'signals': []},
            'S2': {'total': 0, 'wins': 0, 'losses': 0, 'signals': []},
            'S3': {'total': 0, 'wins': 0, 'losses': 0, 'signals': []},
            'S4': {'total': 0, 'wins': 0, 'losses': 0, 'signals': []}
        }
        
        strategies = [
            ('S1', self.engine.evaluate_strategy_1),
            ('S2', self.engine.evaluate_strategy_2),
            ('S3', self.engine.evaluate_strategy_3),
            ('S4', self.engine.evaluate_strategy_4)
        ]
        
        # Test signals on historical data
        for i in range(50, len(df) - lookforward_candles):
            current_df = df.iloc[:i+1].copy()
            current_price = current_df.iloc[-1]['close']
            
            for strategy_name, strategy_func in strategies:
                try:
                    signal, confidence = strategy_func(current_df)
                    
                    if signal != 0:  # Signal detected
                        # Look forward to see if signal was profitable
                        future_prices = df.iloc[i+1:i+1+lookforward_candles]['close']
                        
                        if signal == 1:  # BUY signal
                            max_future_price = future_prices.max()
                            profit = (max_future_price - current_price) / current_price
                            win = profit > 0.0005  # 0.05% profit target
                        else:  # SELL signal
                            min_future_price = future_prices.min()
                            profit = (current_price - min_future_price) / current_price
                            win = profit > 0.0005  # 0.05% profit target
                        
                        results[strategy_name]['total'] += 1
                        if win:
                            results[strategy_name]['wins'] += 1
                        else:
                            results[strategy_name]['losses'] += 1
                        
                        # Store signal details
                        signal_info = {
                            'candle': i,
                            'signal': 'BUY' if signal == 1 else 'SELL',
                            'confidence': confidence,
                            'price': current_price,
                            'result': 'WIN' if win else 'LOSS',
                            'profit': profit
                        }
                        results[strategy_name]['signals'].append(signal_info)
                        
                except Exception as e:
                    continue
        
        return results
    
    def print_accuracy_report(self, results):
        """Print detailed accuracy report"""
        print_colored("📊 SIGNAL ACCURACY REPORT", "HEADER", bold=True)
        print_colored("=" * 60, "HEADER")
        
        total_signals = 0
        total_wins = 0
        
        for strategy, data in results.items():
            if data['total'] > 0:
                accuracy = (data['wins'] / data['total']) * 100
                total_signals += data['total']
                total_wins += data['wins']
                
                print_colored(f"\n{strategy} STRATEGY:", "INFO", bold=True)
                print_colored(f"  Total Signals: {data['total']}", "INFO")
                print_colored(f"  Wins: {data['wins']}", "SUCCESS")
                print_colored(f"  Losses: {data['losses']}", "ERROR")
                print_colored(f"  Accuracy: {accuracy:.1f}%", 
                            "SUCCESS" if accuracy >= 60 else "WARNING" if accuracy >= 50 else "ERROR", bold=True)
                
                # Show recent signals
                if data['signals']:
                    print_colored(f"  Recent Signals:", "INFO")
                    for signal in data['signals'][-3:]:  # Last 3 signals
                        result_color = "SUCCESS" if signal['result'] == 'WIN' else "ERROR"
                        print_colored(f"    {signal['signal']} @ {signal['price']:.5f} "
                                    f"({signal['confidence']:.1%}) - {signal['result']} "
                                    f"({signal['profit']:+.4f})", result_color)
            else:
                print_colored(f"\n{strategy} STRATEGY:", "INFO", bold=True)
                print_colored(f"  No signals generated", "WARNING")
        
        if total_signals > 0:
            overall_accuracy = (total_wins / total_signals) * 100
            print_colored(f"\n🎯 OVERALL PERFORMANCE:", "HEADER", bold=True)
            print_colored(f"  Total Signals: {total_signals}", "INFO")
            print_colored(f"  Total Wins: {total_wins}", "SUCCESS")
            print_colored(f"  Total Losses: {total_signals - total_wins}", "ERROR")
            print_colored(f"  Overall Accuracy: {overall_accuracy:.1f}%", 
                        "SUCCESS" if overall_accuracy >= 60 else "WARNING" if overall_accuracy >= 50 else "ERROR", bold=True)
            
            return overall_accuracy, total_signals
        else:
            print_colored(f"\n⚠️  NO SIGNALS GENERATED", "WARNING", bold=True)
            return 0, 0
    
    def run_accuracy_test(self, pairs=["EUR_USD", "GBP_USD", "USD_JPY"]):
        """Run comprehensive accuracy test"""
        print_colored("🧪 COMPREHENSIVE SIGNAL ACCURACY TEST", "HEADER", bold=True)
        print_colored("=" * 60, "HEADER")
        
        all_results = {}
        
        for pair in pairs:
            print_colored(f"\n📈 Testing {pair}...", "INFO", bold=True)
            df = self.create_realistic_market_data(pair)

            if df is not None and len(df) > 100:
                results = self.test_signal_accuracy(df)
                all_results[pair] = results

                print_colored(f"\n{pair} Results:", "INFO", bold=True)
                accuracy, signals = self.print_accuracy_report(results)

                time.sleep(0.5)  # Brief pause
            else:
                print_colored(f"❌ Failed to get data for {pair}", "ERROR")
        
        return all_results

def main():
    """Main function"""
    tester = SignalAccuracyTester()
    
    print_colored("🎯 STARTING SIGNAL ACCURACY ANALYSIS", "HEADER", bold=True)
    print_colored("This will test current strategy performance on real market data", "INFO")
    print()
    
    # Test on multiple pairs
    results = tester.run_accuracy_test()
    
    print_colored("\n🏁 ACCURACY TEST COMPLETED", "HEADER", bold=True)
    print_colored("Use these results to adjust strategy strictness", "INFO")

if __name__ == "__main__":
    main()
