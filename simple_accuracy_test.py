#!/usr/bin/env python3
"""
Simple Signal Accuracy Test - Test current strategy performance
"""

import pandas as pd
import numpy as np
from strategy_engine import StrategyEngine
from utils import print_colored

def create_market_data():
    """Create realistic market data"""
    print_colored("📊 Creating test market data...", "INFO")
    
    np.random.seed(42)
    base_price = 1.0850
    data = []
    current_price = base_price
    
    # Create 150 candles with realistic patterns
    for i in range(150):
        # Market phases
        if i < 30:  # Sideways
            change = np.random.uniform(-0.00005, 0.00005)
        elif i < 60:  # Uptrend
            change = np.random.uniform(-0.00002, 0.0001)
        elif i < 90:  # Pullback
            change = np.random.uniform(-0.00008, 0.00003)
        elif i < 120:  # Strong trend
            change = np.random.uniform(0.00001, 0.00012)
        else:  # Reversal
            change = np.random.uniform(-0.0001, 0.00002)
        
        open_price = current_price
        close_price = open_price + change
        
        volatility = np.random.uniform(0.00003, 0.0002)
        high_price = max(open_price, close_price) + volatility
        low_price = min(open_price, close_price) - volatility
        
        volume = int(np.random.uniform(1000, 4000))
        
        data.append({
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'volume': volume
        })
        
        current_price = close_price
    
    df = pd.DataFrame(data)
    
    # Add indicators
    df['rsi'] = calculate_rsi(df['close'])
    df['ema_20'] = df['close'].ewm(span=20).mean()
    
    print_colored(f"✅ Created {len(df)} candles", "SUCCESS")
    return df

def calculate_rsi(prices, period=14):
    """Calculate RSI"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi.fillna(50)

def test_strategy_accuracy():
    """Test current strategy accuracy"""
    print_colored("🧪 TESTING CURRENT STRATEGY ACCURACY", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    # Create test data
    df = create_market_data()
    
    # Initialize engine
    engine = StrategyEngine()
    engine.load_models()
    
    strategies = [
        ("S1: MOMENTUM BREAKOUT", engine.evaluate_strategy_1),
        ("S2: PULLBACK ENTRY", engine.evaluate_strategy_2),
        ("S3: REVERSAL SIGNALS", engine.evaluate_strategy_3),
        ("S4: TREND CONFIRMATION", engine.evaluate_strategy_4)
    ]
    
    results = {}
    
    # Test each strategy
    for name, strategy_func in strategies:
        print_colored(f"\n🔍 Testing {name}...", "INFO", bold=True)
        
        signals = []
        wins = 0
        losses = 0
        
        # Test on sliding window
        for i in range(50, len(df) - 5):
            test_df = df.iloc[:i+1].copy()
            
            try:
                signal, confidence = strategy_func(test_df)
                
                if signal != 0:
                    current_price = test_df.iloc[-1]['close']
                    
                    # Check next 5 candles for profit
                    future_prices = df.iloc[i+1:i+6]['close']
                    
                    if signal == 1:  # BUY
                        max_price = future_prices.max()
                        profit = (max_price - current_price) / current_price
                        win = profit > 0.0003  # 0.03% target
                    else:  # SELL
                        min_price = future_prices.min()
                        profit = (current_price - min_price) / current_price
                        win = profit > 0.0003  # 0.03% target
                    
                    signal_info = {
                        'candle': i,
                        'type': 'BUY' if signal == 1 else 'SELL',
                        'confidence': confidence,
                        'price': current_price,
                        'profit': profit,
                        'win': win
                    }
                    signals.append(signal_info)
                    
                    if win:
                        wins += 1
                    else:
                        losses += 1
                        
            except Exception as e:
                continue
        
        # Calculate accuracy
        total = wins + losses
        accuracy = (wins / total * 100) if total > 0 else 0
        
        results[name] = {
            'total': total,
            'wins': wins,
            'losses': losses,
            'accuracy': accuracy,
            'signals': signals
        }
        
        # Print results
        if total > 0:
            print_colored(f"  📊 Total Signals: {total}", "INFO")
            print_colored(f"  ✅ Wins: {wins}", "SUCCESS")
            print_colored(f"  ❌ Losses: {losses}", "ERROR")
            print_colored(f"  🎯 Accuracy: {accuracy:.1f}%", 
                        "SUCCESS" if accuracy >= 60 else "WARNING" if accuracy >= 50 else "ERROR", bold=True)
            
            # Show recent signals
            print_colored(f"  📋 Recent Signals:", "INFO")
            for signal in signals[-3:]:
                result = "WIN" if signal['win'] else "LOSS"
                color = "SUCCESS" if signal['win'] else "ERROR"
                print_colored(f"    {signal['type']} @ {signal['price']:.5f} "
                            f"({signal['confidence']:.1%}) - {result} ({signal['profit']:+.4f})", color)
        else:
            print_colored(f"  ⚠️  No signals generated", "WARNING")
    
    # Overall summary
    total_signals = sum(r['total'] for r in results.values())
    total_wins = sum(r['wins'] for r in results.values())
    
    if total_signals > 0:
        overall_accuracy = (total_wins / total_signals) * 100
        print_colored(f"\n🎯 OVERALL PERFORMANCE:", "HEADER", bold=True)
        print_colored(f"  Total Signals: {total_signals}", "INFO")
        print_colored(f"  Total Wins: {total_wins}", "SUCCESS")
        print_colored(f"  Total Losses: {total_signals - total_wins}", "ERROR")
        print_colored(f"  Overall Accuracy: {overall_accuracy:.1f}%", 
                    "SUCCESS" if overall_accuracy >= 60 else "WARNING" if overall_accuracy >= 50 else "ERROR", bold=True)
        
        # Recommendations
        print_colored(f"\n💡 RECOMMENDATIONS:", "HEADER", bold=True)
        if overall_accuracy < 50:
            print_colored("  ❌ Accuracy too low - need to make strategies MORE STRICT", "ERROR", bold=True)
        elif overall_accuracy < 60:
            print_colored("  ⚠️  Accuracy moderate - slight tightening recommended", "WARNING", bold=True)
        else:
            print_colored("  ✅ Good accuracy - strategies are well balanced", "SUCCESS", bold=True)
        
        return results
    else:
        print_colored(f"\n⚠️  NO SIGNALS GENERATED - strategies too strict", "WARNING", bold=True)
        return results

if __name__ == "__main__":
    test_strategy_accuracy()
