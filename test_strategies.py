#!/usr/bin/env python3

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_engine import StrategyEngine
from config import TRADING_CONFIG

def test_strategies():
    """Test if strategies can generate signals"""
    print("🔧 Testing Strategy Engine...")
    
    try:
        # Initialize strategy engine
        engine = StrategyEngine()
        print("✅ Strategy engine initialized")
        
        # Create sample data
        dates = pd.date_range(start='2025-07-02 11:00', end='2025-07-02 20:00', freq='1min')
        n = len(dates)
        
        # Generate realistic sample data
        np.random.seed(42)
        base_price = 1.0500
        
        # Create realistic OHLCV data
        data = []
        current_price = base_price
        
        for i, date in enumerate(dates):
            # Small random walk
            change = np.random.normal(0, 0.0001)
            current_price += change
            
            # Generate OHLC
            high = current_price + abs(np.random.normal(0, 0.0001))
            low = current_price - abs(np.random.normal(0, 0.0001))
            open_price = current_price + np.random.normal(0, 0.00005)
            close_price = current_price + np.random.normal(0, 0.00005)
            
            # Ensure OHLC logic
            high = max(high, open_price, close_price)
            low = min(low, open_price, close_price)
            
            volume = np.random.randint(100, 1000)
            
            data.append({
                'time': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        print(f"📊 Created sample data with {len(df)} candles")
        print(f"📊 Price range: {df['low'].min():.5f} - {df['high'].max():.5f}")
        
        # Test each strategy
        strategies = ['S1', 'S2', 'S3', 'S4']
        total_signals = 0
        
        for strategy in strategies:
            signals = 0
            print(f"\n🔍 Testing {strategy}...")
            
            for i in range(50, len(df)):  # Start from index 50 to have enough history
                try:
                    signal, confidence = engine.get_signal(df.iloc[:i+1], strategy)
                    if signal != 0:
                        signals += 1
                        print(f"  📈 Signal found at index {i}: {signal} (confidence: {confidence:.2f})")
                        if signals >= 3:  # Limit output
                            break
                except Exception as e:
                    print(f"  ❌ Error at index {i}: {e}")
                    continue
            
            print(f"  📊 {strategy}: {signals} signals found")
            total_signals += signals
        
        print(f"\n📊 TOTAL SIGNALS: {total_signals}")
        print(f"📊 MIN_CONFIDENCE: {TRADING_CONFIG['MIN_CONFIDENCE']}")
        
        if total_signals == 0:
            print("❌ NO SIGNALS GENERATED - Strategies are too strict!")
        else:
            print("✅ Strategies are generating signals")
            
    except Exception as e:
        print(f"❌ Error testing strategies: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_strategies()
