#!/usr/bin/env python3
"""
Trading Bot Launcher
Main entry point for the trading bot system
"""

import sys
import os
from utils import print_colored, print_header, select_currency_pairs

def show_menu():
    """Display the main menu"""
    print_header("🤖 ADVANCED TRADING BOT SYSTEM")
    print_colored("Choose an option:", "INFO", bold=True)
    print()
    print_colored("1. 📈 Live Trading (Ultra High-Accuracy)", "BUY", bold=True)
    print_colored("2. 🔗 Test API Connection", "INFO", bold=True)
    print_colored("3. 📊 View Current Directory", "INFO", bold=True)
    print_colored("4. ❌ Exit", "ERROR", bold=True)
    print()

def test_api_connection():
    """Test the Oanda API connection"""
    print_header("🔗 TESTING API CONNECTION")
    try:
        import test_oanda_connection
        # The test will run automatically when imported
    except Exception as e:
        print_colored(f"❌ Error testing API: {str(e)}", "ERROR")

def start_rule_based_trading():
    """Start the rule-based live trading bot"""
    print_header("📈 RULE-BASED LIVE TRADING")
    print_colored("🔧 Using: Ultra High-Accuracy Strategies (85%+ confidence)", "INFO")
    print_colored("   S1: MOMENTUM BREAKOUT (Continuation) - 90% confidence", "INFO")
    print_colored("   S2: PULLBACK ENTRY (Continuation) - 88% confidence", "INFO")
    print_colored("   S3: REVERSAL SIGNALS (Pure Reversal) - 92% confidence", "INFO")
    print_colored("   S4: TREND CONFIRMATION (Strong Trend Following) - 89% confidence", "INFO")
    print_colored("📊 Signal Generation: Hard-coded technical analysis", "INFO")
    print_colored("⚡ Performance: Fast and transparent", "SUCCESS")
    print_colored("⚠️  This will start live market monitoring", "WARNING", bold=True)
    print_colored("Press Ctrl+C to stop the bot at any time", "INFO")
    print()

    # Select currency pairs
    try:
        selected_pairs = select_currency_pairs()
        print()

        print_colored(f"📊 Selected {len(selected_pairs)} currency pairs for analysis", "INFO")
        print_colored("⚠️  Ready to start live market monitoring", "WARNING", bold=True)
        print()

        confirm = input("Start rule-based live trading? (y/n): ").strip().lower()
        if confirm == 'y':
            try:
                from live_trading_bot import main
                main(selected_pairs)
            except Exception as e:
                print_colored(f"❌ Error starting rule-based trading: {str(e)}", "ERROR")
        else:
            print_colored("❌ Rule-based trading cancelled", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error in currency pair selection: {str(e)}", "ERROR")

def start_ml_integrated_trading():
    """Start the ML-integrated live trading bot"""
    print_header("🧠 ML-INTEGRATED LIVE TRADING")
    print_colored("🤖 Using: Machine Learning + Rule-based strategies", "SUCCESS")
    print_colored("📊 Signal Generation: AI predictions + technical analysis", "SUCCESS")
    print_colored("🎯 Performance: Enhanced accuracy with ML models", "SUCCESS")
    print_colored("⚠️  This will start live market monitoring with AI", "WARNING", bold=True)
    print_colored("Press Ctrl+C to stop the bot at any time", "INFO")
    print()

    # Select currency pairs
    try:
        selected_pairs = select_currency_pairs()
        print()

        print_colored(f"🧠 Selected {len(selected_pairs)} currency pairs for AI analysis", "SUCCESS")
        print_colored("⚠️  Ready to start live market monitoring with ML", "WARNING", bold=True)
        print()

        confirm = input("Start ML-integrated live trading? (y/n): ").strip().lower()
        if confirm == 'y':
            try:
                from ml_trading_bot import main
                main(selected_pairs)
            except Exception as e:
                print_colored(f"❌ Error starting ML-integrated trading: {str(e)}", "ERROR")
        else:
            print_colored("❌ ML-integrated trading cancelled", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error in currency pair selection: {str(e)}", "ERROR")

def start_advanced_signal_generator():
    """Start the advanced signal generator with historical pattern recognition"""
    print_header("🔬 ADVANCED SIGNAL GENERATOR")
    print_colored("🎯 Using: Historical Pattern Recognition", "HEADER")
    print_colored("📊 Analysis: Time-based candle pattern analysis", "HEADER")
    print_colored("🔍 Filters: Trend confirmation + Market structure", "HEADER")
    print_colored("⚡ Performance: Advanced signal scoring system", "SUCCESS")
    print()

    try:
        from advanced_signal_generator import main
        main()
    except Exception as e:
        print_colored(f"❌ Error starting advanced signal generator: {str(e)}", "ERROR")

def show_backtest_menu():
    """Display the backtesting sub-menu"""
    print_header("🔬 BACKTESTING SYSTEM")
    print_colored("Choose backtesting mode:", "INFO", bold=True)
    print()
    print_colored("1. 📈 Rule-Based Backtesting", "BUY", bold=True)
    print_colored("   • Test pure rule-based strategies", "INFO")
    print_colored("   • Fast execution, transparent logic", "INFO")
    print()
    print_colored("2. 🧠 ML-Integrated Backtesting", "SUCCESS", bold=True)
    print_colored("   • Test ML-enhanced strategies", "SUCCESS")
    print_colored("   • AI predictions + rule validation", "SUCCESS")
    print()
    print_colored("3. 🔄 Comparative Backtesting", "HEADER", bold=True)
    print_colored("   • Compare rule-based vs ML performance", "HEADER")
    print_colored("   • Side-by-side analysis", "HEADER")
    print()
    print_colored("4. ⬅️  Back to Main Menu", "WARNING", bold=True)
    print()

def start_backtesting():
    """Start the backtesting system with sub-menu"""
    while True:
        try:
            show_backtest_menu()
            choice = input("Enter your choice (1-4): ").strip()

            if choice == '1':
                start_rule_based_backtest()
            elif choice == '2':
                start_ml_integrated_backtest()
            elif choice == '3':
                start_comparative_backtest()
            elif choice == '4':
                break
            else:
                print_colored("❌ Invalid choice. Please enter 1-4.", "ERROR")

            if choice in ['1', '2', '3']:
                print()
                input("Press Enter to continue...")
                print()

        except KeyboardInterrupt:
            print_colored("\n⚠️  Returning to main menu...", "WARNING")
            break
        except Exception as e:
            print_colored(f"❌ Error in backtesting menu: {str(e)}", "ERROR")

def start_rule_based_backtest():
    """Start rule-based backtesting"""
    print_header("📈 RULE-BASED BACKTESTING")
    print_colored("🔧 Testing: Pure rule-based strategies only", "INFO")
    print_colored("📊 Method: Hard-coded technical analysis", "INFO")
    print()
    try:
        from backtest_system import main
        main()
    except Exception as e:
        print_colored(f"❌ Error starting rule-based backtest: {str(e)}", "ERROR")

def start_ml_integrated_backtest():
    """Start ML-integrated backtesting"""
    print_header("🧠 ML-INTEGRATED BACKTESTING")
    print_colored("🤖 Testing: ML models + rule-based validation", "SUCCESS")
    print_colored("📊 Method: AI predictions with confidence scoring", "SUCCESS")
    print()
    try:
        from ml_backtest_system import main
        main()
    except Exception as e:
        print_colored(f"❌ Error starting ML-integrated backtest: {str(e)}", "ERROR")

def start_comparative_backtest():
    """Start comparative backtesting (rule-based vs ML)"""
    print_header("🔄 COMPARATIVE BACKTESTING")
    print_colored("📊 Testing: Rule-based vs ML-integrated strategies", "HEADER")
    print_colored("🎯 Method: Side-by-side performance comparison", "HEADER")
    print()
    try:
        from comparative_backtest import main
        main()
    except Exception as e:
        print_colored(f"❌ Error starting comparative backtest: {str(e)}", "ERROR")

def view_directory():
    """View current directory contents"""
    print_header("📊 CURRENT DIRECTORY CONTENTS")
    
    try:
        files = os.listdir('.')
        
        # Separate files by type
        python_files = [f for f in files if f.endswith('.py')]
        csv_files = [f for f in files if f.endswith('.csv')]
        pkl_files = [f for f in files if f.endswith('.pkl')]
        directories = [f for f in files if os.path.isdir(f)]
        other_files = [f for f in files if f not in python_files + csv_files + pkl_files + directories]
        
        if python_files:
            print_colored("🐍 Python Files:", "INFO", bold=True)
            for f in sorted(python_files):
                print_colored(f"   {f}", "INFO")
            print()
        
        if csv_files:
            print_colored("📊 CSV Data Files:", "SUCCESS", bold=True)
            for f in sorted(csv_files):
                print_colored(f"   {f}", "SUCCESS")
            print()
        
        if pkl_files:
            print_colored("🤖 Model Files:", "BUY", bold=True)
            for f in sorted(pkl_files):
                print_colored(f"   {f}", "BUY")
            print()
        
        if directories:
            print_colored("📁 Directories:", "HEADER", bold=True)
            for d in sorted(directories):
                print_colored(f"   {d}/", "HEADER")
            print()
        
        if other_files:
            print_colored("📄 Other Files:", "WARNING", bold=True)
            for f in sorted(other_files):
                print_colored(f"   {f}", "WARNING")
            print()
        
        print_colored(f"Total files: {len(files)}", "INFO", bold=True)
        
    except Exception as e:
        print_colored(f"❌ Error viewing directory: {str(e)}", "ERROR")

def main():
    """Main launcher function"""
    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-7): ").strip()

            if choice == '1':
                start_rule_based_trading()
            elif choice == '2':
                start_ml_integrated_trading()
            elif choice == '3':
                start_advanced_signal_generator()
            elif choice == '4':
                start_backtesting()
            elif choice == '5':
                test_api_connection()
            elif choice == '6':
                view_directory()
            elif choice == '7':
                print_colored("👋 Goodbye! ✨ Hope Your Section Was Good", "INFO", bold=True)
                break
            else:
                print_colored("❌ Invalid choice. Please enter 1-7.", "ERROR")

            # Wait for user to continue
            if choice in ['1', '2', '3', '5', '6']:
                print()
                input("Press Enter to continue...")
                print()

        except KeyboardInterrupt:
            print_colored("\n👋 Goodbye! ✨ Hope Your Section Was Good", "INFO", bold=True)
            break
        except Exception as e:
            print_colored(f"❌ Unexpected error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
