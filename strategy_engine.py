#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np

from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG
        self.load_models()
        
    def load_models(self):
        """Initialize strategy engine (no models needed for rule-based strategies)"""
        print_colored("🔧 Initializing rule-based strategy engine...", "INFO")
        print_colored("✅ Strategy engine ready", "SUCCESS")

    def evaluate_strategy_1(self, df):
        """Strategy 1: MOMENTUM BREAKOUT (Continuation Strategy)"""
        if len(df) < 25:  # Increased back to 25 for more data
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Calculate key levels (reduced lookback for more signals)
            lookback = 10  # Reduced from 20 to 10
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)
            resistance = recent_highs.max()
            support = recent_lows.min()

            # Balanced volume confirmation for quality signals
            vol_avg = df['volume'].tail(8).mean()  # Moderate lookback
            volume_confirmed = volume > vol_avg * 1.3  # Moderate volume requirement

            # Momentum indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Body and wick analysis
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low

            # Balanced momentum conditions for quality signals
            strong_bullish = (close > open_ and body > (high - low) * 0.6)  # Strong candle required
            strong_bearish = (close < open_ and body > (high - low) * 0.6)  # Strong candle required

            # Balanced price action confirmation
            prev_close = prev['close']
            momentum_up = close > prev_close * 1.0008  # Moderate upward momentum (0.08%)
            momentum_down = close < prev_close * 0.9992  # Moderate downward momentum (0.08%)

            # Clear breakout levels with small tolerance
            resistance_break = close > resistance * 1.0001  # Must break resistance by 0.01%
            support_break = close < support * 0.9999      # Must break support by 0.01%

            # Trend confirmation: 2 out of 3 candles in direction
            last_3_closes = df['close'].tail(3)
            uptrend_confirmed = sum(last_3_closes.iloc[i] > last_3_closes.iloc[i-1] for i in range(1, len(last_3_closes))) >= 2
            downtrend_confirmed = sum(last_3_closes.iloc[i] < last_3_closes.iloc[i-1] for i in range(1, len(last_3_closes))) >= 2

            # BUY: Quality breakout with confirmations
            if (resistance_break and  # Clear resistance break
                strong_bullish and  # Strong bullish candle
                volume_confirmed and  # Volume confirmation
                momentum_up and  # Upward momentum
                uptrend_confirmed and  # Trend confirmation
                45 <= rsi <= 75):  # Good RSI range
                return 1, 0.92  # High confidence

            # SELL: Quality breakdown with confirmations
            elif (support_break and  # Clear support break
                  strong_bearish and  # Strong bearish candle
                  volume_confirmed and  # Volume confirmation
                  momentum_down and  # Downward momentum
                  downtrend_confirmed and  # Trend confirmation
                  25 <= rsi <= 55):  # Good RSI range
                return -1, 0.92  # High confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df):
        """Strategy 2: PULLBACK ENTRY (Continuation Strategy)"""
        if len(df) < 20:  # Increased back to 20 for better analysis
            return 0, 0.0

        try:
            current = df.iloc[-1]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Get EMA for trend direction
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Balanced volume confirmation for quality signals
            vol_avg = df['volume'].tail(6).mean()  # Moderate lookback
            volume_ok = volume > vol_avg * 1.2  # Moderate volume requirement

            # Body and wick analysis - STRICT requirements
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Balanced pullback detection - reasonably close to EMA
            near_ema = abs(close - ema_20) / ema_20 < 0.003 if ema_20 > 0 else False  # Within 0.3%

            # Balanced trend strength - need good trend
            recent_closes = df['close'].tail(6)  # Moderate lookback
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))

            # EMA slope confirmation
            ema_values = df['ema_20'].tail(4)
            ema_rising = sum(ema_values.iloc[i] > ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 3
            ema_falling = sum(ema_values.iloc[i] < ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 3

            # BUY: Quality pullback in uptrend
            if (close > ema_20 * 0.999 and  # Near or above EMA
                near_ema and  # Close to EMA (pullback)
                close > open_ and  # Current candle bullish
                body_ratio > 0.4 and  # Good candle body
                volume_ok and  # Volume confirmation
                uptrend_strength >= 4 and  # Good uptrend (4 out of 5 up candles)
                ema_rising and  # EMA rising
                35 <= rsi <= 70):  # Good RSI range for pullback
                return 1, 0.91  # High confidence

            # SELL: Quality pullback in downtrend
            elif (close < ema_20 * 1.001 and  # Near or below EMA
                  near_ema and  # Close to EMA (pullback)
                  close < open_ and  # Current candle bearish
                  body_ratio > 0.4 and  # Good candle body
                  volume_ok and  # Volume confirmation
                  downtrend_strength >= 4 and  # Good downtrend (4 out of 5 down candles)
                  ema_falling and  # EMA falling
                  30 <= rsi <= 65):  # Good RSI range for pullback
                return -1, 0.91  # High confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_3(self, df):
        """Strategy 3: REVERSAL SIGNALS (Pure Reversal Strategy)"""
        if len(df) < 30:  # Increased to 30 for better reversal analysis
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # RSI for overbought/oversold
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Balanced volume analysis for quality reversals
            vol_avg = df['volume'].tail(8).mean()  # Moderate lookback
            volume_spike = volume > vol_avg * 1.4  # Good volume requirement for reversals

            # STRICT wick analysis for strong rejection
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            total_range = high - low

            # Balanced rejection requirements
            strong_upper_rejection = upper_wick > body * 2.0 and upper_wick > total_range * 0.35  # Good rejection
            strong_lower_rejection = lower_wick > body * 2.0 and lower_wick > total_range * 0.35  # Good rejection

            # Balanced price levels - moderate lookback for key levels
            recent_high = df['high'].tail(15).max()  # 15 candles
            recent_low = df['low'].tail(15).min()   # 15 candles

            # Near key levels with small tolerance
            near_high = abs(high - recent_high) / recent_high < 0.002 if recent_high > 0 else False  # Within 0.2%
            near_low = abs(low - recent_low) / recent_low < 0.002 if recent_low > 0 else False      # Within 0.2%

            # Balanced momentum exhaustion
            prev_momentum = abs(prev['close'] - prev['open'])
            current_momentum = abs(close - open_)
            momentum_weakening = current_momentum < prev_momentum * 0.7  # Moderate momentum exhaustion

            # Rejection confirmation: at least one test of level
            last_3_highs = df['high'].tail(3)
            last_3_lows = df['low'].tail(3)
            tested_high = any(h >= recent_high * 0.998 for h in last_3_highs)
            tested_low = any(l <= recent_low * 1.002 for l in last_3_lows)

            # SELL: Quality reversal from high with confirmations
            if (rsi >= 70 and  # Overbought
                near_high and  # Near recent high
                strong_upper_rejection and  # Strong rejection wick
                close < open_ and  # Bearish close
                volume_spike and  # Volume spike
                momentum_weakening and  # Momentum weakening
                tested_high):  # Level has been tested
                return -1, 0.93  # High confidence

            # BUY: Quality reversal from low with confirmations
            elif (rsi <= 30 and  # Oversold
                  near_low and  # Near recent low
                  strong_lower_rejection and  # Strong rejection wick
                  close > open_ and  # Bullish close
                  volume_spike and  # Volume spike
                  momentum_weakening and  # Momentum weakening
                  tested_low):  # Level has been tested
                return 1, 0.93  # High confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df):
        """Strategy 4: TREND CONFIRMATION (Strong Trend Following)"""
        if len(df) < 25:  # Increased to 25 for strong trend analysis
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Technical indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close

            # Balanced volume confirmation for good trends
            vol_avg = df['volume'].tail(8).mean()  # Moderate lookback
            volume_confirmed = volume > vol_avg * 1.2  # Moderate volume requirement

            # Balanced trend strength analysis - need good trends
            recent_closes = df['close'].tail(7)  # 7 candles for trend analysis
            recent_highs = df['high'].tail(7)
            recent_lows = df['low'].tail(7)

            # Good uptrend confirmation
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            higher_lows = sum(recent_lows.iloc[i] > recent_lows.iloc[i-1] for i in range(1, len(recent_lows)))

            # Good downtrend confirmation
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            lower_highs = sum(recent_highs.iloc[i] < recent_highs.iloc[i-1] for i in range(1, len(recent_highs)))

            # STRICT price action strength
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Balanced candle confirmation
            strong_bullish = (close > open_ and body_ratio > 0.5)  # Good candle required
            strong_bearish = (close < open_ and body_ratio > 0.5)  # Good candle required

            # Balanced EMA trend confirmation
            above_ema = close > ema_20 * 1.001  # Clearly above EMA
            below_ema = close < ema_20 * 0.999  # Clearly below EMA

            # EMA slope and momentum confirmation
            ema_values = df['ema_20'].tail(4)
            ema_rising = sum(ema_values.iloc[i] > ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 3
            ema_falling = sum(ema_values.iloc[i] < ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 3

            # Price momentum confirmation
            price_momentum_up = close > df['close'].iloc[-4] * 1.002  # 0.2% move in 4 candles
            price_momentum_down = close < df['close'].iloc[-4] * 0.998  # 0.2% move in 4 candles

            # BUY: Quality trend confirmation
            if (uptrend_strength >= 5 and  # 5+ up candles out of 6
                higher_lows >= 4 and  # Good higher lows pattern
                strong_bullish and  # Strong current candle
                above_ema and  # Above EMA20
                volume_confirmed and  # Volume confirmation
                ema_rising and  # EMA rising
                price_momentum_up and  # Price momentum
                45 <= rsi <= 75):  # Good RSI for trend continuation
                return 1, 0.91  # High confidence

            # SELL: Quality trend confirmation
            elif (downtrend_strength >= 5 and  # 5+ down candles out of 6
                  lower_highs >= 4 and  # Good lower highs pattern
                  strong_bearish and  # Strong current candle
                  below_ema and  # Below EMA20
                  volume_confirmed and  # Volume confirmation
                  ema_falling and  # EMA falling
                  price_momentum_down and  # Price momentum
                  25 <= rsi <= 55):  # Good RSI for trend continuation
                return -1, 0.91  # High confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df):
        """Evaluate all strategies and return combined signal"""
        signals = {}
        
        # Evaluate each strategy
        s1_signal, s1_conf = self.evaluate_strategy_1(df)
        s2_signal, s2_conf = self.evaluate_strategy_2(df)
        s3_signal, s3_conf = self.evaluate_strategy_3(df)
        s4_signal, s4_conf = self.evaluate_strategy_4(df)
        
        signals = {
            'S1': {'signal': s1_signal, 'confidence': s1_conf},
            'S2': {'signal': s2_signal, 'confidence': s2_conf},
            'S3': {'signal': s3_signal, 'confidence': s3_conf},
            'S4': {'signal': s4_signal, 'confidence': s4_conf}
        }
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            signal_name = 'BUY' if best_signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': best_confidence,
                'strategy': best_strategy,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi




