#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np
import joblib
from datetime import datetime
from config import STRATEGY_CONFIG, MODEL_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.feature_columns = None
        self.load_models()
        
    def load_models(self):
        """Load trained models and preprocessing components"""
        try:
            print_colored("🔄 Loading trained models...", "INFO")
            
            # Load model components
            self.model = joblib.load(MODEL_CONFIG["MODEL_PATH"])
            self.scaler = joblib.load(MODEL_CONFIG["SCALER_PATH"])
            self.label_encoder = joblib.load(MODEL_CONFIG["LABEL_ENCODER_PATH"])
            self.feature_columns = joblib.load(MODEL_CONFIG["FEATURES_PATH"])
            
            print_colored("✅ Models loaded successfully", "SUCCESS")
            
        except Exception as e:
            print_colored(f"❌ Error loading models: {str(e)}", "ERROR")
            raise

    def evaluate_strategy_1(self, df):
        """Evaluate Strategy 1: Breakout with Volume & Confirmed Momentum"""
        if len(df) < 50:
            return 0, 0.0

        try:
            current = df.iloc[-1]

            # Get current candle values
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Calculate support and resistance levels (last 30-40 candles)
            lookback = 35
            recent_data = df.tail(lookback)
            resistance = recent_data['high'].max()
            support = recent_data['low'].min()

            # Volume analysis
            volume_avg_10 = df['volume'].tail(10).mean()
            volume_spike = volume > (volume_avg_10 * 1.2)

            # Calculate wicks and body
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low

            # Clean breakout conditions
            clean_breakout_up = (upper_wick == 0) or (upper_wick < body * 0.25)
            clean_breakdown = (lower_wick == 0) or (lower_wick < body * 0.25)

            # Room to move (next resistance/support at least 0.6% away)
            room_up = (resistance - close) / close >= 0.006 if close > 0 else False
            room_down = (close - support) / close >= 0.006 if close > 0 else False

            # RSI and MACD momentum confirmation
            rsi_4 = self.calculate_rsi(df['close'], period=4).iloc[-1] if len(df) >= 4 else 50
            macd_hist = self.calculate_macd_histogram(df['close']).iloc[-1] if len(df) >= 26 else 0

            # BUY Conditions (Breakout Above Resistance)
            if (close > resistance and  # Price breaks above resistance
                close > open_ and  # Current candle closes above resistance (no wick rejection)
                volume_spike and  # Volume spike
                clean_breakout_up and  # Clean breakout
                room_up and  # Room to move
                40 <= rsi_4 <= 70 and  # RSI not overbought
                macd_hist > 0):  # MACD histogram rising
                return 1, 0.85  # BUY signal with high confidence

            # SELL Conditions (Breakout Below Support)
            elif (close < support and  # Price breaks below support
                  close < open_ and  # Current candle closes below support (no lower wick rejection)
                  volume_spike and  # Volume spike
                  clean_breakdown and  # Clean breakdown
                  room_down and  # Room to move
                  30 <= rsi_4 <= 60 and  # RSI not oversold
                  macd_hist < 0):  # MACD histogram falling
                return -1, 0.85  # SELL signal with high confidence

            return 0, 0.0  # No signal

        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df):
        """Evaluate Strategy 2: Order Block Rejection with Volume Drop"""
        if len(df) < 20:
            return 0, 0.0

        try:
            current = df.iloc[-1]

            # Get current candle values
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Calculate wicks and body
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low

            # Trend context detection
            trend_context = self.detect_trend_context(df, lookback=10)

            # Volume analysis (5-candle average)
            volume_avg_5 = df['volume'].tail(5).mean()
            volume_drop = volume < (volume_avg_5 * 0.8)

            # Stochastic confirmation
            stoch_k, stoch_d = self.calculate_stochastic(df, k_period=5, d_period=3)
            if len(stoch_k) >= 2 and len(stoch_d) >= 2:
                stoch_bearish_cross = (stoch_k.iloc[-2] > stoch_d.iloc[-2] and
                                     stoch_k.iloc[-1] < stoch_d.iloc[-1])
                stoch_bullish_cross = (stoch_k.iloc[-2] < stoch_d.iloc[-2] and
                                     stoch_k.iloc[-1] > stoch_d.iloc[-1])
            else:
                stoch_bearish_cross = False
                stoch_bullish_cross = False

            # Detect order blocks (supply/demand zones)
            resistance_levels, support_levels = self.detect_support_resistance_levels(df)

            # SELL Conditions (Rejection at Supply Zone)
            if trend_context == 1:  # Uptrend context
                for supply_level in resistance_levels:
                    # Check if price touches supply zone
                    if abs(high - supply_level) / supply_level <= 0.001:  # Within 0.1%
                        # Rejection wick condition
                        if (upper_wick > body * 1.5 and  # Upper wick > 1.5x candle body
                            close < supply_level and  # Candle closes outside the order block
                            volume_drop and  # Volume drop
                            stoch_bearish_cross):  # Stochastic bearish crossover
                            return -1, 0.8  # SELL signal

            # BUY Conditions (Rejection at Demand Zone)
            elif trend_context == -1:  # Downtrend context
                for demand_level in support_levels:
                    # Check if price touches demand zone
                    if abs(low - demand_level) / demand_level <= 0.001:  # Within 0.1%
                        # Rejection wick condition
                        if (lower_wick > body * 1.5 and  # Lower wick > 1.5x candle body
                            close > demand_level and  # Candle closes outside the order block
                            volume_drop and  # Volume drop
                            stoch_bullish_cross):  # Stochastic bullish crossover
                            return 1, 0.8  # BUY signal

            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_3(self, df):
        """Evaluate Strategy 3: Triple Zone Rejection with Volume Divergence"""
        if len(df) < 40:
            return 0, 0.0

        try:
            current = df.iloc[-1]

            # Get current candle values
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Calculate wicks and body
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low

            # Detect key support and resistance zones
            resistance_levels, support_levels = self.detect_support_resistance_levels(df, lookback=40)

            # EMA(8) for confirmation
            ema_8 = self.calculate_ema(df['close'], period=8)
            ema_turning_down = (len(ema_8) >= 2 and ema_8.iloc[-1] < ema_8.iloc[-2])
            ema_turning_up = (len(ema_8) >= 2 and ema_8.iloc[-1] > ema_8.iloc[-2])

            # Check for third rejection at resistance levels
            for resistance_zone in resistance_levels:
                # Count previous rejections at this zone (within 0.1% tolerance)
                rejection_count = 0
                zone_volumes = []

                for i in range(len(df) - 40, len(df) - 1):  # Last 40 candles
                    candle = df.iloc[i]
                    if abs(candle['high'] - resistance_zone) / resistance_zone <= 0.001:
                        rejection_count += 1
                        zone_volumes.append(candle['volume'])

                # SELL Conditions (3rd Rejection at Resistance)
                if (rejection_count >= 2 and  # Price rejected twice before
                    abs(high - resistance_zone) / resistance_zone <= 0.001 and  # Third touch
                    upper_wick > body and  # Upper wick > candle body
                    close > open_ and  # Candle closes green (still in trend)
                    volume < (sum(zone_volumes) / len(zone_volumes) * 0.85) if zone_volumes else True and  # Volume drop
                    ema_turning_down):  # EMA(8) starts turning down
                    return -1, 0.85  # SELL signal

            # Check for third rejection at support levels
            for support_zone in support_levels:
                # Count previous rejections at this zone
                rejection_count = 0
                zone_volumes = []

                for i in range(len(df) - 40, len(df) - 1):  # Last 40 candles
                    candle = df.iloc[i]
                    if abs(candle['low'] - support_zone) / support_zone <= 0.001:
                        rejection_count += 1
                        zone_volumes.append(candle['volume'])

                # BUY Conditions (3rd Rejection at Support)
                if (rejection_count >= 2 and  # Price rejected twice before
                    abs(low - support_zone) / support_zone <= 0.001 and  # Third touch
                    lower_wick > body and  # Lower wick > candle body
                    close < open_ and  # Candle closes red (still in trend)
                    volume < (sum(zone_volumes) / len(zone_volumes) * 0.85) if zone_volumes else True and  # Volume drop
                    ema_turning_up):  # EMA(8) starts turning up
                    return 1, 0.85  # BUY signal

            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df):
        """Evaluate Strategy 4: Trendline Break & Fakeout Reversal"""
        if len(df) < 30:
            return 0, 0.0

        try:
            current = df.iloc[-1]

            # Get current candle values
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Calculate wicks and body
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low

            # Detect trendlines using linear regression
            trendlines = self.detect_trendlines(df)

            if not trendlines:
                return 0, 0.0

            # Volume analysis for breakout average
            breakout_volume_avg = df['volume'].tail(10).mean()
            volume_drop = volume < (breakout_volume_avg * 0.8)

            # RSI(3) for divergence detection
            rsi_3 = self.calculate_rsi(df['close'], period=3)
            if len(rsi_3) < 3:
                return 0, 0.0

            # Hidden divergence detection
            hidden_bullish_div = self.detect_hidden_bullish_divergence(df, rsi_3)
            hidden_bearish_div = self.detect_hidden_bearish_divergence(df, rsi_3)

            # BUY Conditions (Downtrend Reversal)
            for trendline in trendlines['downtrend']:
                # Check if price breaks below downtrend line
                if (low < trendline['value'] and  # Price breaks below trendline
                    lower_wick > body * 2 and  # Lower wick > 2x candle body (strong rejection)
                    volume_drop and  # Volume drop
                    hidden_bullish_div and  # Hidden bullish RSI(3) divergence
                    close > trendline['value']):  # Price holds above trendline
                    return 1, 0.85  # BUY signal

            # SELL Conditions (Uptrend Reversal)
            for trendline in trendlines['uptrend']:
                # Check if price breaks above uptrend line
                if (high > trendline['value'] and  # Price breaks above trendline
                    upper_wick > body * 2 and  # Upper wick > 2x candle body
                    volume_drop and  # Volume drop
                    hidden_bearish_div and  # Hidden bearish RSI(3) divergence
                    close < trendline['value']):  # Price holds below trendline
                    return -1, 0.85  # SELL signal

            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df):
        """Evaluate all strategies and return combined signal"""
        signals = {}
        
        # Evaluate each strategy
        s1_signal, s1_conf = self.evaluate_strategy_1(df)
        s2_signal, s2_conf = self.evaluate_strategy_2(df)
        s3_signal, s3_conf = self.evaluate_strategy_3(df)
        s4_signal, s4_conf = self.evaluate_strategy_4(df)
        
        signals = {
            'S1': {'signal': s1_signal, 'confidence': s1_conf},
            'S2': {'signal': s2_signal, 'confidence': s2_conf},
            'S3': {'signal': s3_signal, 'confidence': s3_conf},
            'S4': {'signal': s4_signal, 'confidence': s4_conf}
        }
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            signal_name = 'BUY' if best_signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': best_confidence,
                'strategy': best_strategy,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def calculate_macd_histogram(self, prices, fast=12, slow=26, signal=9):
        """Calculate MACD histogram"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        histogram = macd - macd_signal
        return histogram

    def calculate_stochastic(self, df, k_period=5, d_period=3):
        """Calculate Stochastic oscillator"""
        high = df['high']
        low = df['low']
        close = df['close']

        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()

        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()

        return k_percent, d_percent

    def calculate_ema(self, prices, period=8):
        """Calculate Exponential Moving Average"""
        return prices.ewm(span=period).mean()

    def detect_trend_context(self, df, lookback=10):
        """Detect trend context (higher highs/lows or lower highs/lows)"""
        if len(df) < lookback:
            return 0

        recent_data = df.tail(lookback)
        highs = recent_data['high'].values
        lows = recent_data['low'].values

        # Check for higher highs and higher lows (uptrend)
        higher_highs = sum(highs[i] > highs[i-1] for i in range(1, len(highs)))
        higher_lows = sum(lows[i] > lows[i-1] for i in range(1, len(lows)))

        # Check for lower highs and lower lows (downtrend)
        lower_highs = sum(highs[i] < highs[i-1] for i in range(1, len(highs)))
        lower_lows = sum(lows[i] < lows[i-1] for i in range(1, len(lows)))

        uptrend_score = higher_highs + higher_lows
        downtrend_score = lower_highs + lower_lows

        if uptrend_score > downtrend_score and uptrend_score >= lookback * 0.6:
            return 1  # Uptrend
        elif downtrend_score > uptrend_score and downtrend_score >= lookback * 0.6:
            return -1  # Downtrend
        else:
            return 0  # Sideways/unclear

    def detect_support_resistance_levels(self, df, lookback=40):
        """Detect support and resistance levels"""
        if len(df) < lookback:
            return [], []

        recent_data = df.tail(lookback)

        # Find local highs and lows
        highs = []
        lows = []

        for i in range(2, len(recent_data) - 2):
            current_high = recent_data.iloc[i]['high']
            current_low = recent_data.iloc[i]['low']

            # Check if it's a local high
            if (current_high > recent_data.iloc[i-1]['high'] and
                current_high > recent_data.iloc[i-2]['high'] and
                current_high > recent_data.iloc[i+1]['high'] and
                current_high > recent_data.iloc[i+2]['high']):
                highs.append(current_high)

            # Check if it's a local low
            if (current_low < recent_data.iloc[i-1]['low'] and
                current_low < recent_data.iloc[i-2]['low'] and
                current_low < recent_data.iloc[i+1]['low'] and
                current_low < recent_data.iloc[i+2]['low']):
                lows.append(current_low)

        return sorted(set(highs), reverse=True)[:3], sorted(set(lows))[:3]  # Top 3 levels

    def detect_trendlines(self, df, lookback=20):
        """Detect trendlines using linear regression"""
        if len(df) < lookback:
            return {'uptrend': [], 'downtrend': []}

        recent_data = df.tail(lookback)

        # Find swing highs and lows
        swing_highs = []
        swing_lows = []

        for i in range(1, len(recent_data) - 1):
            current_high = recent_data.iloc[i]['high']
            current_low = recent_data.iloc[i]['low']

            # Check for swing high
            if (current_high > recent_data.iloc[i-1]['high'] and
                current_high > recent_data.iloc[i+1]['high']):
                swing_highs.append((i, current_high))

            # Check for swing low
            if (current_low < recent_data.iloc[i-1]['low'] and
                current_low < recent_data.iloc[i+1]['low']):
                swing_lows.append((i, current_low))

        trendlines = {'uptrend': [], 'downtrend': []}

        # Calculate uptrend lines (connecting swing lows)
        if len(swing_lows) >= 2:
            x_vals = [point[0] for point in swing_lows]
            y_vals = [point[1] for point in swing_lows]

            if len(x_vals) >= 2:
                slope, intercept = np.polyfit(x_vals, y_vals, 1)
                if slope > 0:  # Upward sloping trendline
                    current_value = slope * (len(recent_data) - 1) + intercept
                    trendlines['uptrend'].append({'value': current_value, 'slope': slope})

        # Calculate downtrend lines (connecting swing highs)
        if len(swing_highs) >= 2:
            x_vals = [point[0] for point in swing_highs]
            y_vals = [point[1] for point in swing_highs]

            if len(x_vals) >= 2:
                slope, intercept = np.polyfit(x_vals, y_vals, 1)
                if slope < 0:  # Downward sloping trendline
                    current_value = slope * (len(recent_data) - 1) + intercept
                    trendlines['downtrend'].append({'value': current_value, 'slope': slope})

        return trendlines

    def detect_hidden_bullish_divergence(self, df, rsi):
        """Detect hidden bullish divergence"""
        if len(df) < 10 or len(rsi) < 10:
            return False

        try:
            # Look for higher lows in price but lower lows in RSI
            recent_lows = df['low'].tail(10)
            recent_rsi = rsi.tail(10)

            # Find the last two significant lows
            price_lows = []
            rsi_lows = []

            for i in range(1, len(recent_lows) - 1):
                if (recent_lows.iloc[i] < recent_lows.iloc[i-1] and
                    recent_lows.iloc[i] < recent_lows.iloc[i+1]):
                    price_lows.append(recent_lows.iloc[i])
                    rsi_lows.append(recent_rsi.iloc[i])

            if len(price_lows) >= 2 and len(rsi_lows) >= 2:
                # Check for higher low in price and lower low in RSI
                return (price_lows[-1] > price_lows[-2] and rsi_lows[-1] < rsi_lows[-2])

            return False
        except:
            return False

    def detect_hidden_bearish_divergence(self, df, rsi):
        """Detect hidden bearish divergence"""
        if len(df) < 10 or len(rsi) < 10:
            return False

        try:
            # Look for lower highs in price but higher highs in RSI
            recent_highs = df['high'].tail(10)
            recent_rsi = rsi.tail(10)

            # Find the last two significant highs
            price_highs = []
            rsi_highs = []

            for i in range(1, len(recent_highs) - 1):
                if (recent_highs.iloc[i] > recent_highs.iloc[i-1] and
                    recent_highs.iloc[i] > recent_highs.iloc[i+1]):
                    price_highs.append(recent_highs.iloc[i])
                    rsi_highs.append(recent_rsi.iloc[i])

            if len(price_highs) >= 2 and len(rsi_highs) >= 2:
                # Check for lower high in price and higher high in RSI
                return (price_highs[-1] < price_highs[-2] and rsi_highs[-1] > rsi_highs[-2])

            return False
        except:
            return False
