#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np

from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG
        self.load_models()
        
    def load_models(self):
        """Initialize strategy engine (no models needed for rule-based strategies)"""
        print_colored("🔧 Initializing rule-based strategy engine...", "INFO")
        print_colored("✅ Strategy engine ready", "SUCCESS")

    def evaluate_strategy_1(self, df):
        """Strategy 1: MOMENTUM BREAKOUT (Continuation Strategy)"""
        if len(df) < 15:  # Reduced to 15 for more frequent signals
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Calculate key levels (reduced lookback for more signals)
            lookback = 10  # Reduced from 20 to 10
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)
            resistance = recent_highs.max()
            support = recent_lows.min()

            # Moderate volume confirmation for more signals
            vol_avg = df['volume'].tail(5).mean()  # Shorter lookback
            volume_confirmed = volume > vol_avg * 1.1  # Lower volume requirement

            # Momentum indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Body and wick analysis
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low

            # Moderate momentum conditions for more signals
            strong_bullish = (close > open_ and body > (high - low) * 0.4)  # Moderate candle required
            strong_bearish = (close < open_ and body > (high - low) * 0.4)  # Moderate candle required

            # Moderate price action confirmation
            prev_close = prev['close']
            momentum_up = close > prev_close * 1.0005  # Light upward momentum (0.05%)
            momentum_down = close < prev_close * 0.9995  # Light downward momentum (0.05%)

            # Near breakout levels with tolerance for more signals
            near_resistance = close >= resistance * 0.9998  # Near resistance (within 0.02%)
            near_support = close <= support * 1.0002      # Near support (within 0.02%)

            # Light trend confirmation: 1 out of 2 candles in direction
            last_2_closes = df['close'].tail(2)
            uptrend_hint = last_2_closes.iloc[-1] > last_2_closes.iloc[-2]
            downtrend_hint = last_2_closes.iloc[-1] < last_2_closes.iloc[-2]

            # BUY: Moderate breakout signals for frequency
            if (near_resistance and  # Near resistance
                strong_bullish and  # Moderate bullish candle
                volume_confirmed and  # Volume confirmation
                momentum_up and  # Light upward momentum
                uptrend_hint and  # Light trend hint
                40 <= rsi <= 80):  # Wider RSI range
                return 1, 0.88  # Good confidence

            # SELL: Moderate breakdown signals for frequency
            elif (near_support and  # Near support
                  strong_bearish and  # Moderate bearish candle
                  volume_confirmed and  # Volume confirmation
                  momentum_down and  # Light downward momentum
                  downtrend_hint and  # Light trend hint
                  20 <= rsi <= 60):  # Wider RSI range
                return -1, 0.88  # Good confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df):
        """Strategy 2: PULLBACK ENTRY (Now rejects false signals from upper/lower wicks)"""
        if len(df) < 12:
            return 0, 0.0

        try:
            current = df.iloc[-1]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']

            # Indicators
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close
            recent_closes = df['close'].tail(4)
            ema_values = df['ema_20'].tail(4)

            # Candle structure
            body = abs(close - open_)
            total_range = high - low
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            body_ratio = body / total_range if total_range > 0 else 0

            near_ema = abs(close - ema_20) / ema_20 < 0.01 if ema_20 > 0 else False  # Within 1%

            # Trend strength
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))

            ema_rising = sum(ema_values.iloc[i] > ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 3
            ema_falling = sum(ema_values.iloc[i] < ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 3

            price_above_ema = close > ema_20 * 1.0005  # Much lighter requirement
            price_below_ema = close < ema_20 * 0.9995  # Much lighter requirement

            # Light volatility filter (reduced threshold)
            recent_range = (df['high'] - df['low']).tail(5).mean()
            if recent_range < 0.0001 * close:  # Much lower threshold
                return 0, 0.0

            # Light consolidation filter (reduced threshold)
            last10_range = df['high'].tail(10).max() - df['low'].tail(10).min()
            if last10_range < 0.001 * close:  # Much lower threshold
                return 0, 0.0

            # ❗ False rejection filters (NEW)
            if close > open_ and upper_wick > body:
                return 0, 0.0  # Avoid fake bullish candle with upper rejection
            if close < open_ and lower_wick > body:
                return 0, 0.0  # Avoid fake bearish candle with lower rejection

            # BUY Conditions
            if (
                close > open_ and
                near_ema and
                price_above_ema and
                body_ratio > 0.3 and
                lower_wick > upper_wick and
                uptrend_strength >= 2 and
                ema_rising
            ):
                return 1, 0.92

            # SELL Conditions
            elif (
                close < open_ and
                near_ema and
                price_below_ema and
                body_ratio > 0.3 and
                upper_wick > lower_wick and
                downtrend_strength >= 2 and
                ema_falling
            ):
                return -1, 0.92

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_3(self, df):
        """Strategy 3: REVERSAL SIGNALS (Pure Reversal Strategy)"""
        if len(df) < 15:  # Reduced for more frequent signals
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # RSI for overbought/oversold
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Light volume analysis for more signals
            vol_avg = df['volume'].tail(5).mean()  # Shorter lookback
            volume_spike = volume > vol_avg * 1.1  # Light volume requirement

            # STRICT wick analysis for strong rejection
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            total_range = high - low

            # Light rejection requirements for more signals
            upper_rejection = upper_wick > body * 1.2 and upper_wick > total_range * 0.25  # Moderate rejection
            lower_rejection = lower_wick > body * 1.2 and lower_wick > total_range * 0.25  # Moderate rejection

            # Shorter lookback for more frequent key levels
            recent_high = df['high'].tail(8).max()  # 8 candles
            recent_low = df['low'].tail(8).min()   # 8 candles

            # Wider tolerance for key levels
            near_high = abs(high - recent_high) / recent_high < 0.005 if recent_high > 0 else False  # Within 0.5%
            near_low = abs(low - recent_low) / recent_low < 0.005 if recent_low > 0 else False      # Within 0.5%

            # Light momentum exhaustion for more signals
            prev_momentum = abs(prev['close'] - prev['open'])
            current_momentum = abs(close - open_)
            momentum_weakening = current_momentum < prev_momentum * 0.8  # Light momentum exhaustion

            # Light level testing confirmation
            last_2_highs = df['high'].tail(2)
            last_2_lows = df['low'].tail(2)
            tested_high = any(h >= recent_high * 0.995 for h in last_2_highs)
            tested_low = any(l <= recent_low * 1.005 for l in last_2_lows)

            # SELL: Light reversal signals for more frequency
            if (rsi >= 65 and  # Moderately overbought
                near_high and  # Near recent high
                upper_rejection and  # Moderate rejection wick
                close < open_ and  # Bearish close
                volume_spike and  # Light volume spike
                momentum_weakening and  # Light momentum weakening
                tested_high):  # Level has been tested
                return -1, 0.86  # Good confidence

            # BUY: Light reversal signals for more frequency
            elif (rsi <= 35 and  # Moderately oversold
                  near_low and  # Near recent low
                  lower_rejection and  # Moderate rejection wick
                  close > open_ and  # Bullish close
                  volume_spike and  # Light volume spike
                  momentum_weakening and  # Light momentum weakening
                  tested_low):  # Level has been tested
                return 1, 0.86  # Good confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df):
        """Strategy 4: TREND CONFIRMATION (Strong Trend Following)"""
        if len(df) < 12:  # Reduced for more frequent signals
            return 0, 0.0

        try:
            current = df.iloc[-1]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Technical indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close

            # Light volume confirmation for more signals
            vol_avg = df['volume'].tail(5).mean()  # Shorter lookback
            volume_confirmed = volume > vol_avg * 1.0  # Light volume requirement

            # Light trend strength analysis - need some trend
            recent_closes = df['close'].tail(5)  # 5 candles for trend analysis
            recent_highs = df['high'].tail(5)
            recent_lows = df['low'].tail(5)

            # Light uptrend confirmation
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            higher_lows = sum(recent_lows.iloc[i] > recent_lows.iloc[i-1] for i in range(1, len(recent_lows)))

            # Light downtrend confirmation
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            lower_highs = sum(recent_highs.iloc[i] < recent_highs.iloc[i-1] for i in range(1, len(recent_highs)))

            # STRICT price action strength
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Light candle confirmation
            strong_bullish = (close > open_ and body_ratio > 0.3)  # Moderate candle required
            strong_bearish = (close < open_ and body_ratio > 0.3)  # Moderate candle required

            # Light EMA trend confirmation
            above_ema = close > ema_20 * 1.0005  # Slightly above EMA
            below_ema = close < ema_20 * 0.9995  # Slightly below EMA

            # Light EMA slope and momentum confirmation
            ema_values = df['ema_20'].tail(3)
            ema_rising = sum(ema_values.iloc[i] > ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 2
            ema_falling = sum(ema_values.iloc[i] < ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 2

            # Light price momentum confirmation
            price_momentum_up = close > df['close'].iloc[-3] * 1.001  # 0.1% move in 3 candles
            price_momentum_down = close < df['close'].iloc[-3] * 0.999  # 0.1% move in 3 candles

            # BUY: Light trend confirmation for more signals
            if (uptrend_strength >= 3 and  # 3+ up candles out of 4
                higher_lows >= 2 and  # Some higher lows pattern
                strong_bullish and  # Moderate current candle
                above_ema and  # Above EMA20
                volume_confirmed and  # Light volume confirmation
                ema_rising and  # EMA rising
                price_momentum_up and  # Light price momentum
                40 <= rsi <= 80):  # Wider RSI for trend continuation
                return 1, 0.85  # Good confidence

            # SELL: Light trend confirmation for more signals
            elif (downtrend_strength >= 3 and  # 3+ down candles out of 4
                  lower_highs >= 2 and  # Some lower highs pattern
                  strong_bearish and  # Moderate current candle
                  below_ema and  # Below EMA20
                  volume_confirmed and  # Light volume confirmation
                  ema_falling and  # EMA falling
                  price_momentum_down and  # Light price momentum
                  20 <= rsi <= 60):  # Wider RSI for trend continuation
                return -1, 0.85  # Good confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df):
        """Evaluate all strategies and return combined signal"""
        signals = {}
        
        # Evaluate each strategy
        s1_signal, s1_conf = self.evaluate_strategy_1(df)
        s2_signal, s2_conf = self.evaluate_strategy_2(df)
        s3_signal, s3_conf = self.evaluate_strategy_3(df)
        s4_signal, s4_conf = self.evaluate_strategy_4(df)
        
        signals = {
            'S1': {'signal': s1_signal, 'confidence': s1_conf},
            'S2': {'signal': s2_signal, 'confidence': s2_conf},
            'S3': {'signal': s3_signal, 'confidence': s3_conf},
            'S4': {'signal': s4_signal, 'confidence': s4_conf}
        }
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            signal_name = 'BUY' if best_signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': best_confidence,
                'strategy': best_strategy,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi




