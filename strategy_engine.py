#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np

from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG
        self.load_models()
        
    def load_models(self):
        """Initialize strategy engine (no models needed for rule-based strategies)"""
        print_colored("🔧 Initializing rule-based strategy engine...", "INFO")
        print_colored("✅ Strategy engine ready", "SUCCESS")

    def evaluate_strategy_1(self, df):
        """Strategy 1: MOMENTUM BREAKOUT (Continuation Strategy)"""
        if len(df) < 30:
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Calculate key levels (20 candles for more frequent signals)
            lookback = 20
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)
            resistance = recent_highs.max()
            support = recent_lows.min()

            # Volume confirmation (must be above average)
            vol_avg = df['volume'].tail(5).mean()
            volume_confirmed = volume > vol_avg * 1.1

            # Momentum indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Body and wick analysis
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low

            # Strong momentum conditions
            strong_bullish = (close > open_ and body > (high - low) * 0.6 and upper_wick < body * 0.3)
            strong_bearish = (close < open_ and body > (high - low) * 0.6 and lower_wick < body * 0.3)

            # Price action confirmation
            prev_close = prev['close']
            momentum_up = close > prev_close * 1.0001  # At least 0.01% move
            momentum_down = close < prev_close * 0.9999  # At least 0.01% move

            # BUY: Strong bullish breakout above resistance
            if (close > resistance and  # Break resistance
                strong_bullish and  # Strong bullish candle
                volume_confirmed and  # Volume confirmation
                momentum_up and  # Upward momentum
                45 <= rsi <= 75):  # RSI in momentum zone
                return 1, 0.90

            # SELL: Strong bearish breakdown below support
            elif (close < support and  # Break support
                  strong_bearish and  # Strong bearish candle
                  volume_confirmed and  # Volume confirmation
                  momentum_down and  # Downward momentum
                  25 <= rsi <= 55):  # RSI in momentum zone
                return -1, 0.90

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df):
        """Strategy 2: PULLBACK ENTRY (Continuation Strategy)"""
        if len(df) < 15:
            return 0, 0.0

        try:
            current = df.iloc[-1]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Get EMA for trend direction
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Volume confirmation
            vol_avg = df['volume'].tail(3).mean()
            volume_ok = volume > vol_avg * 0.8  # Not too low volume

            # Body and wick analysis
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Pullback detection (price near EMA after trend)
            near_ema = abs(close - ema_20) / ema_20 < 0.002 if ema_20 > 0 else False  # Within 0.2%

            # Trend strength (last 5 candles)
            recent_closes = df['close'].tail(5)
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))

            # BUY: Pullback in uptrend (price bounces off EMA)
            if (close > ema_20 and  # Above EMA (uptrend)
                near_ema and  # Near EMA (pullback)
                close > open_ and  # Current candle bullish
                body_ratio > 0.4 and  # Strong body
                volume_ok and  # Volume confirmation
                uptrend_strength >= 3 and  # Strong uptrend
                30 <= rsi <= 65):  # RSI not overbought
                return 1, 0.88

            # SELL: Pullback in downtrend (price bounces off EMA)
            elif (close < ema_20 and  # Below EMA (downtrend)
                  near_ema and  # Near EMA (pullback)
                  close < open_ and  # Current candle bearish
                  body_ratio > 0.4 and  # Strong body
                  volume_ok and  # Volume confirmation
                  downtrend_strength >= 3 and  # Strong downtrend
                  35 <= rsi <= 70):  # RSI not oversold
                return -1, 0.88

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_3(self, df):
        """Strategy 3: REVERSAL SIGNALS (Pure Reversal Strategy)"""
        if len(df) < 20:
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # RSI for overbought/oversold
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Volume analysis
            vol_avg = df['volume'].tail(5).mean()
            volume_spike = volume > vol_avg * 1.2

            # Wick analysis for rejection
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            total_range = high - low

            # Strong rejection wicks
            strong_upper_rejection = upper_wick > body * 2 and upper_wick > total_range * 0.4
            strong_lower_rejection = lower_wick > body * 2 and lower_wick > total_range * 0.4

            # Price levels (10 candles for more frequent signals)
            recent_high = df['high'].tail(10).max()
            recent_low = df['low'].tail(10).min()

            # Near key levels
            near_high = abs(high - recent_high) / recent_high < 0.001 if recent_high > 0 else False
            near_low = abs(low - recent_low) / recent_low < 0.001 if recent_low > 0 else False

            # Momentum exhaustion
            prev_momentum = abs(prev['close'] - prev['open'])
            current_momentum = abs(close - open_)
            momentum_weakening = current_momentum < prev_momentum * 0.7

            # SELL: Reversal from high (overbought rejection)
            if (rsi >= 70 and  # Overbought
                near_high and  # Near recent high
                strong_upper_rejection and  # Strong upper wick rejection
                close < open_ and  # Bearish close
                volume_spike and  # Volume confirmation
                momentum_weakening):  # Momentum weakening
                return -1, 0.92

            # BUY: Reversal from low (oversold rejection)
            elif (rsi <= 30 and  # Oversold
                  near_low and  # Near recent low
                  strong_lower_rejection and  # Strong lower wick rejection
                  close > open_ and  # Bullish close
                  volume_spike and  # Volume confirmation
                  momentum_weakening):  # Momentum weakening
                return 1, 0.92

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df):
        """Strategy 4: TREND CONFIRMATION (Strong Trend Following)"""
        if len(df) < 15:
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Technical indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close

            # Volume confirmation
            vol_avg = df['volume'].tail(5).mean()
            volume_confirmed = volume > vol_avg * 1.1

            # Trend strength analysis (last 5 candles)
            recent_closes = df['close'].tail(5)
            recent_highs = df['high'].tail(5)
            recent_lows = df['low'].tail(5)

            # Strong uptrend confirmation
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            higher_lows = sum(recent_lows.iloc[i] > recent_lows.iloc[i-1] for i in range(1, len(recent_lows)))

            # Strong downtrend confirmation
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            lower_highs = sum(recent_highs.iloc[i] < recent_highs.iloc[i-1] for i in range(1, len(recent_highs)))

            # Price action strength
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Strong candle confirmation
            strong_bullish = (close > open_ and body_ratio > 0.5)
            strong_bearish = (close < open_ and body_ratio > 0.5)

            # EMA trend confirmation
            above_ema = close > ema_20
            below_ema = close < ema_20

            # BUY: Strong uptrend confirmation
            if (uptrend_strength >= 4 and  # 4+ up candles in last 5
                higher_lows >= 3 and  # Higher lows pattern
                strong_bullish and  # Strong current candle
                above_ema and  # Above EMA20
                volume_confirmed and  # Volume confirmation
                45 <= rsi <= 75):  # RSI in trend zone
                return 1, 0.89

            # SELL: Strong downtrend confirmation
            elif (downtrend_strength >= 4 and  # 4+ down candles in last 5
                  lower_highs >= 3 and  # Lower highs pattern
                  strong_bearish and  # Strong current candle
                  below_ema and  # Below EMA20
                  volume_confirmed and  # Volume confirmation
                  25 <= rsi <= 55):  # RSI in trend zone
                return -1, 0.89

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df):
        """Evaluate all strategies and return combined signal"""
        signals = {}
        
        # Evaluate each strategy
        s1_signal, s1_conf = self.evaluate_strategy_1(df)
        s2_signal, s2_conf = self.evaluate_strategy_2(df)
        s3_signal, s3_conf = self.evaluate_strategy_3(df)
        s4_signal, s4_conf = self.evaluate_strategy_4(df)
        
        signals = {
            'S1': {'signal': s1_signal, 'confidence': s1_conf},
            'S2': {'signal': s2_signal, 'confidence': s2_conf},
            'S3': {'signal': s3_signal, 'confidence': s3_conf},
            'S4': {'signal': s4_signal, 'confidence': s4_conf}
        }
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            signal_name = 'BUY' if best_signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': best_confidence,
                'strategy': best_strategy,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi




