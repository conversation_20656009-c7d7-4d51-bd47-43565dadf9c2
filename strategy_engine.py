#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np

from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG
        self.load_models()
        
    def load_models(self):
        """Initialize strategy engine (no models needed for rule-based strategies)"""
        print_colored("🔧 Initializing rule-based strategy engine...", "INFO")
        print_colored("✅ Strategy engine ready", "SUCCESS")

    def evaluate_strategy_1(self, df):
        """Strategy 1: MOMENTUM BREAKOUT (Continuation Strategy)"""
        if len(df) < 10:  # Reduced from 30 to 10
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Calculate key levels (reduced lookback for more signals)
            lookback = 10  # Reduced from 20 to 10
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)
            resistance = recent_highs.max()
            support = recent_lows.min()

            # Volume confirmation (more lenient)
            vol_avg = df['volume'].tail(3).mean()  # Reduced from 5 to 3
            volume_confirmed = volume > vol_avg * 0.8  # Reduced from 1.1 to 0.8

            # Momentum indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Body and wick analysis
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low

            # More lenient momentum conditions
            strong_bullish = (close > open_ and body > (high - low) * 0.3)  # Reduced from 0.6 to 0.3
            strong_bearish = (close < open_ and body > (high - low) * 0.3)  # Reduced from 0.6 to 0.3

            # Price action confirmation (more lenient)
            prev_close = prev['close']
            momentum_up = close > prev_close * 1.00005  # Reduced from 0.01% to 0.005%
            momentum_down = close < prev_close * 0.99995  # Reduced from 0.01% to 0.005%

            # Near breakout levels (more sensitive)
            near_resistance = close >= resistance * 0.9998  # Within 0.02% of resistance
            near_support = close <= support * 1.0002      # Within 0.02% of support

            # BUY: Near or above resistance with bullish momentum
            if (near_resistance and  # Near or above resistance
                strong_bullish and  # Strong bullish candle
                volume_confirmed and  # Volume confirmation
                momentum_up and  # Upward momentum
                35 <= rsi <= 85):  # Wider RSI range
                return 1, 0.87  # Slightly lower confidence for more signals

            # SELL: Near or below support with bearish momentum
            elif (near_support and  # Near or below support
                  strong_bearish and  # Strong bearish candle
                  volume_confirmed and  # Volume confirmation
                  momentum_down and  # Downward momentum
                  15 <= rsi <= 65):  # Wider RSI range
                return -1, 0.87  # Slightly lower confidence for more signals

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df):
        """Strategy 2: PULLBACK ENTRY (Continuation Strategy)"""
        if len(df) < 8:  # Reduced from 15 to 8
            return 0, 0.0

        try:
            current = df.iloc[-1]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Get EMA for trend direction
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Volume confirmation (more lenient)
            vol_avg = df['volume'].tail(2).mean()  # Reduced from 3 to 2
            volume_ok = volume > vol_avg * 0.6  # Reduced from 0.8 to 0.6

            # Body and wick analysis (more lenient)
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Pullback detection (more lenient - wider range from EMA)
            near_ema = abs(close - ema_20) / ema_20 < 0.005 if ema_20 > 0 else False  # Within 0.5% (was 0.2%)

            # Trend strength (last 3 candles - reduced from 5)
            recent_closes = df['close'].tail(3)
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))

            # BUY: Pullback in uptrend (more lenient conditions)
            if (close >= ema_20 * 0.998 and  # Near or above EMA (more lenient)
                close > open_ and  # Current candle bullish
                body_ratio > 0.2 and  # Reduced from 0.4 to 0.2
                volume_ok and  # Volume confirmation
                uptrend_strength >= 1 and  # Reduced from 3 to 1
                25 <= rsi <= 75):  # Wider RSI range
                return 1, 0.85  # Slightly lower confidence for more signals

            # SELL: Pullback in downtrend (more lenient conditions)
            elif (close <= ema_20 * 1.002 and  # Near or below EMA (more lenient)
                  close < open_ and  # Current candle bearish
                  body_ratio > 0.2 and  # Reduced from 0.4 to 0.2
                  volume_ok and  # Volume confirmation
                  downtrend_strength >= 1 and  # Reduced from 3 to 1
                  25 <= rsi <= 75):  # Wider RSI range
                return -1, 0.85  # Slightly lower confidence for more signals

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_3(self, df):
        """Strategy 3: REVERSAL SIGNALS (Pure Reversal Strategy)"""
        if len(df) < 8:  # Reduced from 20 to 8
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # RSI for overbought/oversold
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Volume analysis (more lenient)
            vol_avg = df['volume'].tail(3).mean()  # Reduced from 5 to 3
            volume_spike = volume > vol_avg * 0.9  # Reduced from 1.2 to 0.9

            # Wick analysis for rejection (more lenient)
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            total_range = high - low

            # More lenient rejection wicks
            strong_upper_rejection = upper_wick > body * 1.2 and upper_wick > total_range * 0.25  # Reduced requirements
            strong_lower_rejection = lower_wick > body * 1.2 and lower_wick > total_range * 0.25  # Reduced requirements

            # Price levels (reduced lookback for more signals)
            recent_high = df['high'].tail(6).max()  # Reduced from 10 to 6
            recent_low = df['low'].tail(6).min()   # Reduced from 10 to 6

            # Near key levels (wider range)
            near_high = abs(high - recent_high) / recent_high < 0.003 if recent_high > 0 else False  # Increased from 0.001 to 0.003
            near_low = abs(low - recent_low) / recent_low < 0.003 if recent_low > 0 else False      # Increased from 0.001 to 0.003

            # Momentum exhaustion (more lenient)
            prev_momentum = abs(prev['close'] - prev['open'])
            current_momentum = abs(close - open_)
            momentum_weakening = current_momentum < prev_momentum * 0.9  # Reduced from 0.7 to 0.9

            # SELL: Reversal from high (more lenient conditions)
            if (rsi >= 65 and  # Reduced from 70 to 65
                near_high and  # Near recent high
                (strong_upper_rejection or upper_wick > body) and  # More lenient rejection
                close < open_ and  # Bearish close
                volume_spike):  # Volume confirmation (removed momentum_weakening)
                return -1, 0.88  # Slightly lower confidence for more signals

            # BUY: Reversal from low (more lenient conditions)
            elif (rsi <= 35 and  # Increased from 30 to 35
                  near_low and  # Near recent low
                  (strong_lower_rejection or lower_wick > body) and  # More lenient rejection
                  close > open_ and  # Bullish close
                  volume_spike):  # Volume confirmation (removed momentum_weakening)
                return 1, 0.88  # Slightly lower confidence for more signals

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df):
        """Strategy 4: TREND CONFIRMATION (Strong Trend Following)"""
        if len(df) < 6:  # Reduced from 15 to 6
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Technical indicators
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close

            # Volume confirmation (more lenient)
            vol_avg = df['volume'].tail(3).mean()  # Reduced from 5 to 3
            volume_confirmed = volume > vol_avg * 0.8  # Reduced from 1.1 to 0.8

            # Trend strength analysis (last 3 candles - reduced from 5)
            recent_closes = df['close'].tail(3)
            recent_highs = df['high'].tail(3)
            recent_lows = df['low'].tail(3)

            # More lenient uptrend confirmation
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            higher_lows = sum(recent_lows.iloc[i] > recent_lows.iloc[i-1] for i in range(1, len(recent_lows))) if len(recent_lows) > 1 else 0

            # More lenient downtrend confirmation
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            lower_highs = sum(recent_highs.iloc[i] < recent_highs.iloc[i-1] for i in range(1, len(recent_highs))) if len(recent_highs) > 1 else 0

            # Price action strength (more lenient)
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # More lenient candle confirmation
            strong_bullish = (close > open_ and body_ratio > 0.3)  # Reduced from 0.5 to 0.3
            strong_bearish = (close < open_ and body_ratio > 0.3)  # Reduced from 0.5 to 0.3

            # EMA trend confirmation (more lenient)
            above_ema = close >= ema_20 * 0.999  # Within 0.1% of EMA
            below_ema = close <= ema_20 * 1.001  # Within 0.1% of EMA

            # BUY: Trend confirmation (more lenient)
            if (uptrend_strength >= 2 and  # 2+ up candles in last 3 (was 4 in 5)
                strong_bullish and  # Strong current candle
                above_ema and  # Above EMA20
                volume_confirmed and  # Volume confirmation
                35 <= rsi <= 85):  # Wider RSI range
                return 1, 0.86  # Slightly lower confidence for more signals

            # SELL: Trend confirmation (more lenient)
            elif (downtrend_strength >= 2 and  # 2+ down candles in last 3 (was 4 in 5)
                  strong_bearish and  # Strong current candle
                  below_ema and  # Below EMA20
                  volume_confirmed and  # Volume confirmation
                  15 <= rsi <= 65):  # Wider RSI range
                return -1, 0.86  # Slightly lower confidence for more signals

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df):
        """Evaluate all strategies and return combined signal"""
        signals = {}
        
        # Evaluate each strategy
        s1_signal, s1_conf = self.evaluate_strategy_1(df)
        s2_signal, s2_conf = self.evaluate_strategy_2(df)
        s3_signal, s3_conf = self.evaluate_strategy_3(df)
        s4_signal, s4_conf = self.evaluate_strategy_4(df)
        
        signals = {
            'S1': {'signal': s1_signal, 'confidence': s1_conf},
            'S2': {'signal': s2_signal, 'confidence': s2_conf},
            'S3': {'signal': s3_signal, 'confidence': s3_conf},
            'S4': {'signal': s4_signal, 'confidence': s4_conf}
        }
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            signal_name = 'BUY' if best_signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': best_confidence,
                'strategy': best_strategy,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi




