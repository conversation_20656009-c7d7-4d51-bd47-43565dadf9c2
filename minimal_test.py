#!/usr/bin/env python3
"""
Minimal test to check strategies
"""

import pandas as pd
import numpy as np

# Create test data
data = []
for i in range(50):
    data.append({
        'open': 1.0850 + i * 0.0001,
        'high': 1.0850 + i * 0.0001 + 0.0002,
        'low': 1.0850 + i * 0.0001 - 0.0001,
        'close': 1.0850 + i * 0.0001 + 0.00005,
        'volume': 2000 + i * 10
    })

df = pd.DataFrame(data)
df['rsi'] = 50 + np.sin(np.arange(len(df)) * 0.3) * 20
df['ema_20'] = df['close'].ewm(span=20).mean()

print(f"Test data created: {len(df)} candles")
print(f"RSI range: {df['rsi'].min():.1f} - {df['rsi'].max():.1f}")
print(f"Price range: {df['close'].min():.5f} - {df['close'].max():.5f}")

# Try importing strategy engine
try:
    from strategy_engine import StrategyEngine
    print("✅ Strategy engine imported successfully")
    
    engine = StrategyEngine()
    print("✅ Engine created successfully")
    
    # Test one strategy
    signal, conf = engine.evaluate_strategy_1(df)
    print(f"✅ S1 test: Signal={signal}, Confidence={conf:.2f}")
    
except Exception as e:
    print(f"❌ Error: {str(e)}")
    import traceback
    traceback.print_exc()
