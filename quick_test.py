#!/usr/bin/env python3

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """Quick test to see if strategies work"""
    print("🔧 Quick Strategy Test...")
    
    try:
        from strategy_engine import StrategyEngine
        from config import TRADING_CONFIG
        
        print("✅ Imports successful")
        print(f"📊 MIN_CONFIDENCE: {TRADING_CONFIG['MIN_CONFIDENCE']}")
        
        # Initialize strategy engine
        engine = StrategyEngine()
        print("✅ Strategy engine initialized")
        
        # Create very simple test data that should trigger signals
        dates = pd.date_range(start='2025-07-02 11:00', end='2025-07-02 11:10', freq='1min')
        
        # Create data with clear patterns
        data = []
        base_price = 1.0500
        
        for i, date in enumerate(dates):
            # Create a clear uptrend pattern
            price = base_price + (i * 0.0001)  # Rising prices
            
            data.append({
                'time': date,
                'open': price - 0.00005,
                'high': price + 0.0001,
                'low': price - 0.0001,
                'close': price,
                'volume': 500 + (i * 50)  # Rising volume
            })
        
        df = pd.DataFrame(data)
        print(f"📊 Created {len(df)} candles with clear uptrend")
        print(f"📊 Price: {df['close'].iloc[0]:.5f} -> {df['close'].iloc[-1]:.5f}")
        
        # Add technical indicators
        from utils import add_technical_indicators
        df = add_technical_indicators(df)
        print("✅ Technical indicators added")
        
        # Test all strategies
        print(f"\n🔍 Testing all strategies...")
        try:
            result = engine.evaluate_all_strategies(df)
            print(f"  📊 Result: {result}")

            if result and result['signal'] != 0:
                print(f"  ✅ Signal generated: {result['signal']} (confidence: {result['confidence']:.3f})")
                print(f"  📊 Strategy: {result['strategy']}")
            else:
                print(f"  ❌ No signal generated")

        except Exception as e:
            print(f"  ❌ Error: {e}")
            import traceback
            traceback.print_exc()

        # Test individual strategies
        strategies = [
            ('S1', engine.evaluate_strategy_1),
            ('S2', engine.evaluate_strategy_2),
            ('S3', engine.evaluate_strategy_3),
            ('S4', engine.evaluate_strategy_4)
        ]

        for strategy_name, strategy_func in strategies:
            print(f"\n🔍 Testing {strategy_name} individually...")
            try:
                signal, confidence = strategy_func(df)
                print(f"  📊 Signal: {signal}, Confidence: {confidence:.3f}")

                if signal != 0:
                    print(f"  ✅ {strategy_name} generated signal!")
                else:
                    print(f"  ❌ {strategy_name} no signal")

            except Exception as e:
                print(f"  ❌ Error in {strategy_name}: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n" + "="*50)
        print("🔧 Testing with even more obvious patterns...")
        
        # Create VERY obvious patterns
        obvious_data = []
        for i in range(20):
            price = 1.0500 + (i * 0.0005)  # Much bigger moves
            
            obvious_data.append({
                'time': pd.Timestamp('2025-07-02 12:00') + pd.Timedelta(minutes=i),
                'open': price - 0.0001,
                'high': price + 0.0002,
                'low': price - 0.0002,
                'close': price,
                'volume': 1000 + (i * 100)  # Much higher volume
            })
        
        obvious_df = pd.DataFrame(obvious_data)
        obvious_df = add_technical_indicators(obvious_df)
        
        print(f"📊 Obvious pattern: {obvious_df['close'].iloc[0]:.5f} -> {obvious_df['close'].iloc[-1]:.5f}")
        
        print(f"\n🔍 Testing all strategies with obvious pattern...")
        try:
            result = engine.evaluate_all_strategies(obvious_df)
            print(f"  📊 Result: {result}")

            if result and result['signal'] != 0:
                print(f"  ✅ Signal generated: {result['signal']} (confidence: {result['confidence']:.3f})")
                print(f"  📊 Strategy: {result['strategy']}")
            else:
                print(f"  ❌ Still no signal generated")

        except Exception as e:
            print(f"  ❌ Error: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
