#!/usr/bin/env python3
"""
Quick test to verify strategies are working and providing signals
"""

import pandas as pd
import numpy as np
from strategy_engine import StrategyEngine
from utils import print_colored

def create_test_data():
    """Create test market data that should trigger signals"""
    print_colored("📊 Creating test data designed to trigger signals...", "INFO")
    
    # Create 50 candles of test data
    np.random.seed(42)  # For reproducible results
    
    base_price = 1.1000
    data = []
    
    for i in range(50):
        # Create varying market conditions
        if i < 10:
            # Initial sideways movement
            open_price = base_price + np.random.uniform(-0.0005, 0.0005)
            close_price = open_price + np.random.uniform(-0.0003, 0.0003)
        elif i < 20:
            # Uptrend for momentum signals
            open_price = base_price + (i-10) * 0.0002 + np.random.uniform(-0.0002, 0.0002)
            close_price = open_price + np.random.uniform(0.0001, 0.0005)  # Mostly bullish
        elif i < 30:
            # Pullback for pullback signals
            open_price = base_price + 0.002 - (i-20) * 0.0001 + np.random.uniform(-0.0002, 0.0002)
            close_price = open_price + np.random.uniform(-0.0002, 0.0002)
        elif i < 40:
            # Strong trend for trend confirmation
            open_price = base_price + 0.001 + (i-30) * 0.0003 + np.random.uniform(-0.0001, 0.0001)
            close_price = open_price + np.random.uniform(0.0002, 0.0006)  # Strong bullish
        else:
            # Reversal setup
            open_price = base_price + 0.004 - (i-40) * 0.0002 + np.random.uniform(-0.0002, 0.0002)
            close_price = open_price + np.random.uniform(-0.0004, -0.0001)  # Bearish
        
        high_price = max(open_price, close_price) + np.random.uniform(0, 0.0003)
        low_price = min(open_price, close_price) - np.random.uniform(0, 0.0003)
        volume = np.random.uniform(1000, 5000)
        
        data.append({
            'timestamp': f"2024-01-01 00:{i:02d}:00",
            'open': round(open_price, 5),
            'high': round(high_price, 5),
            'low': round(low_price, 5),
            'close': round(close_price, 5),
            'volume': int(volume)
        })
    
    df = pd.DataFrame(data)
    
    # Add technical indicators
    df['rsi'] = 50 + np.random.uniform(-20, 20, len(df))  # Random RSI around 50
    df['ema_20'] = df['close'].rolling(window=min(20, len(df))).mean().fillna(df['close'])
    
    # Adjust RSI for different market conditions
    df.loc[10:19, 'rsi'] = np.random.uniform(45, 75, 10)  # Uptrend RSI
    df.loc[30:39, 'rsi'] = np.random.uniform(40, 80, 10)  # Strong trend RSI
    df.loc[40:49, 'rsi'] = np.random.uniform(65, 85, 10)  # Overbought for reversal
    
    print_colored(f"✅ Created {len(df)} candles of test data", "SUCCESS")
    return df

def test_strategies():
    """Test all strategies with the test data"""
    print_colored("🧪 Testing Enhanced Strategies for Signal Generation", "HEADER", bold=True)
    print()
    
    # Create test data
    df = create_test_data()
    
    # Initialize strategy engine
    print_colored("🔧 Initializing strategy engine...", "INFO")
    engine = StrategyEngine()
    engine.load_models()  # This just prints initialization message
    
    print_colored("🔍 Testing strategies on multiple data points...", "INFO")
    print()
    
    signal_count = 0
    
    # Test on last 20 candles to see if we get any signals
    for i in range(30, len(df)):
        test_df = df.iloc[:i+1].copy()
        
        # Test each strategy
        strategies = [
            ("S1: MOMENTUM BREAKOUT", engine.evaluate_strategy_1),
            ("S2: PULLBACK ENTRY", engine.evaluate_strategy_2),
            ("S3: REVERSAL SIGNALS", engine.evaluate_strategy_3),
            ("S4: TREND CONFIRMATION", engine.evaluate_strategy_4)
        ]
        
        for name, strategy_func in strategies:
            try:
                signal, confidence = strategy_func(test_df)
                if signal != 0:
                    signal_count += 1
                    signal_type = "BUY" if signal == 1 else "SELL"
                    print_colored(f"🎯 SIGNAL DETECTED!", "SUCCESS", bold=True)
                    print_colored(f"   Strategy: {name}", "INFO")
                    print_colored(f"   Signal: {signal_type}", "BUY" if signal == 1 else "SELL")
                    print_colored(f"   Confidence: {confidence:.1%}", "INFO")
                    print_colored(f"   Candle: {i+1}/{len(df)}", "INFO")
                    print_colored(f"   Price: {test_df.iloc[-1]['close']}", "INFO")
                    print()
            except Exception as e:
                print_colored(f"❌ Error testing {name}: {str(e)}", "ERROR")
    
    print_colored("📊 SUMMARY:", "HEADER", bold=True)
    print_colored(f"Total signals generated: {signal_count}", "SUCCESS" if signal_count > 0 else "ERROR", bold=True)
    
    if signal_count == 0:
        print_colored("⚠️  No signals detected - strategies may be too strict", "WARNING")
        print_colored("💡 Consider further reducing thresholds", "INFO")
    else:
        print_colored(f"✅ Strategies are working! Generated {signal_count} signals", "SUCCESS")
    
    return signal_count > 0

if __name__ == "__main__":
    try:
        success = test_strategies()
        if success:
            print_colored("🎉 Test completed successfully - strategies are generating signals!", "SUCCESS", bold=True)
        else:
            print_colored("❌ Test failed - no signals generated", "ERROR", bold=True)
    except Exception as e:
        print_colored(f"❌ Test error: {str(e)}", "ERROR")
